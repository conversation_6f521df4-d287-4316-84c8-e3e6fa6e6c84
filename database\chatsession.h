#ifndef CHATSESSION_H
#define CHATSESSION_H

#include <QString>
#include <QJsonArray>
#include <QJsonObject>
#include <QStringList>
#include <QMap>
#include <QList>

#include "chatcontext/contextitem.h"

namespace CodeBooster::Internal{

struct ChatSessionBrief
{
    QString uuid;
    QString title;
    qint64 modifiedTime;
    int messageCount = 0;
};

/********************************************************************
 ChatSession
 聊天对话上下文
*********************************************************************/
class ChatSession
{
    friend class ChatDatabase; // 声明 ChatDatabase 为友元类

public:
    enum Role
    {
        User = 1,
        Assistant = 2
    };
    static QString readableTime(int timeStamp);

public:
    ChatSession(const QString &sysMsg = QString());

    QString uuid() const {return mUuid;}
    QString title() const {return mTitle;}

    void setChatTitle(const QString &title);

    QJsonArray chatStorage() const;
    QString messageSource(int index) const;

    QString allContextToJsonString() const;
    void loadContextsFromJsonString(const QString &contexJsonString);

    void appendAssistantMessage(const QString &msg, const QString &model);

    QJsonArray getChatMessage(int maxMessageCount = 1, bool useSysMsg = true);
    QJsonArray getChatMessage(const QString &msg, const QList<ContextItem> &contexts, int maxMessageCount = 1);
    QJsonArray getChatMessage(const QString &sysMsg, const QString &userMsg, const QList<ContextItem> &contexts);

    QList<ContextItem> getMessageContextsById(QString id);

    QString getLastAssistantMessageContent() const;
    QString getLastUserMessageContent() const;

    void appendUserMessage(const QString &msg, const QList<ContextItem> &contexts = QList<ContextItem>());

    QString readableTime() const;

    ChatSessionBrief toBrief() const;
    QJsonObject getMessageObjByFromIndex(int index);

private:
    QJsonObject systemMsg() const;
    QJsonObject makeContentObject(Role role, const QString &msg) const;


private:
    QString mUuid;
    QString mTitle;

    QJsonArray mChatStorage;
    QStringList mMessageSourceNames; ///< 记录消息的来源，主要是为了记录每条回答的模型的名称
    QMap<QString, QList<ContextItem>> mContexts;

    qint64 mModifiedTime;

    QJsonObject mSysMsg;
};

}
#endif // CHATSESSION_H
