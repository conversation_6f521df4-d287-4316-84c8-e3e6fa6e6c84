#ifndef CODESNIPPETWIDGET_H
#define CODESNIPPETWIDGET_H

#include <QFrame>
#include <QVBoxLayout>
#include <QToolBar>
#include <QLabel>
#include <QAction>
#include <QResizeEvent>

class NotePreviewWidget;

namespace CodeBooster::Internal {

class CustomLineWidget;

/**
 * @brief The CodeSnippetWidget class 代码段控件
 */
class CodeSnippetWidget : public QFrame
{
    Q_OBJECT
public:
    explicit CodeSnippetWidget(QWidget *parent = nullptr);

public:
    void showCodeSnippet(const QString &fileName, const QString &selectedText, int startLine = 1, int endLine = 1);
    QString codeSnippet() const;
    void clear();

    // 置顶相关功能
    bool isPinned() const { return mIsPinned; }
    void setPinned(bool pinned);

protected:
    void resizeEvent(QResizeEvent *event) override;
    bool eventFilter(QObject *watched, QEvent *event);

signals:
    void heightChanged(int height);
    void pinStateChanged(bool pinned); // 置顶状态变化信号
    void closeRequested(); // 关闭请求信号

private slots:
    void onActionCloseTriggered();
    void onActionExpandTriggered();
    void onActionPinTriggered(); // 置顶按钮槽函数

private:
    void updateFileNameDisplay(); // 更新文件名显示（处理省略）

private:
    bool mCodeMode;
    bool mIsPinned; // 置顶状态

    QVBoxLayout *mLayout;

    QToolBar *mToolBar;
    QLabel *mFileIcon;
    QLabel *mFileNameTitle;
    QAction *mActionClose;
    QAction *mActionExpand;
    QAction *mActionPin; // 置顶按钮

    CustomLineWidget *mHorLine;

    NotePreviewWidget *mPreviewWgt;
    QString mFileName;
    QString mCodeSnippet;
    int mStartLine; // 开始行号
    int mEndLine;   // 结束行号
};

}

#endif // CODESNIPPETWIDGET_H
