#include "chathistorypage.h"
#include "ui_chathistorypage.h"

#include <QMessageBox>
#include <utils/utilsicons.h>
#include <utils/fancylineedit.h>

#include "chatsessionwgt.h"
#include "common/codeboosterutils.h"
#include "database/chatdatabase.h"

#include "utility/instrumentor.h"

namespace CodeBooster::Internal{

const int ChatHistoryPage::CountPerLoading = 30;

ChatHistoryPage::ChatHistoryPage(const QString &sessionUuid, QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::ChatHistoryPage)
{
    ui->setupUi(this);

    ui->lineEdit_search->setFiltering(true);
    ui->lineEdit_search->setPlaceholderText("请输入关键字");
    connect(ui->lineEdit_search, &QLineEdit::returnPressed, this, &ChatHistoryPage::onSearchSession);
    connect(ui->lineEdit_search, &Utils::FancyLineEdit::rightButtonClicked, this, [this] {
        ui->label_info->setVisible(false);
        ui->stackedWidget->setCurrentWidget(ui->page_sessionList);
    });


    connect(ui->pushButton_search, &QPushButton::clicked, this, &ChatHistoryPage::onSearchSession);
    connect(ui->pushButton_deleteAll, &QPushButton::clicked, this, &ChatHistoryPage::onDeleteAllBtnClicked);

    mSearchSpinner = new SpinnerSolution::Spinner(SpinnerSolution::SpinnerSize::Small, ui->lineEdit_search);
    mSearchSpinner->setVisible(false);

    mPageSpinner = new SpinnerSolution::Spinner(SpinnerSolution::SpinnerSize::Large, this);
    mPageSpinner->setVisible(false);

    // 默认隐藏消息标签
    ui->label_info->setVisible(false);

    // 对话列表页面
    {
        // 初始化加载对话按钮
        mBtnLoadMoreSession = new QPushButton(this);
        mBtnLoadMoreSession->setMinimumSize(100, 30);
        connect(mBtnLoadMoreSession, &QPushButton::clicked, this, &ChatHistoryPage::onLoadMoreBtnClicked);

        // 隐藏滚动区边框
        ui->scrollArea->setStyleSheet("QScrollArea { border: none; }");
        mScrollLayout = new QVBoxLayout(ui->scrollAreaWidgetContents);
        mScrollLayout->setContentsMargins(6, 6, 6, 6);
        mScrollLayout->addWidget(mBtnLoadMoreSession);
        mScrollLayout->insertStretch(-1);
    }

    // 搜索结果页面
    {
        // 初始化加载对话按钮
        mBtnLoadMoreResult = new QPushButton(this);
        mBtnLoadMoreResult->setMinimumSize(100, 30);
        connect(mBtnLoadMoreResult, &QPushButton::clicked, this, &ChatHistoryPage::onShowMoreResultBtnClicked);

        // 隐藏滚动区边框
        ui->scrollArea_searchList->setStyleSheet("QScrollArea { border: none; }");
        mResultLayout = new QVBoxLayout(ui->scrollAreaWidgetContents_2);
        mResultLayout->setContentsMargins(6, 6, 6, 6);
        mResultLayout->addWidget(mBtnLoadMoreResult);
        mResultLayout->insertStretch(-1);
    }

    // 默认显示对话列表页面
    ui->stackedWidget->setCurrentWidget(ui->page_sessionList);
    connect(ui->stackedWidget, &QStackedWidget::currentChanged, this, &ChatHistoryPage::onStackedWidgetChanged);

    // 初始化功能按钮状态
    ui->pushButton_check->setVisible(true);
    ui->pushButton_check->setCheckable(true);
    ui->pushButton_check->setChecked(false);
    connect(ui->pushButton_check, &QPushButton::clicked, this, &ChatHistoryPage::onCheckBtnClicked);

    ui->pushButton_selectAll->setVisible(false);
    connect(ui->pushButton_selectAll, &QPushButton::clicked, this, &ChatHistoryPage::onSelectAllBtnClicked);

    ui->pushButton_invertSelection->setVisible(false);
    connect(ui->pushButton_invertSelection, &QPushButton::clicked, this, &ChatHistoryPage::onInvertSelectionBtnClicked);

    ui->pushButton_deleteSelection->setVisible(false);
    connect(ui->pushButton_deleteSelection, &QPushButton::clicked, this, &ChatHistoryPage::onDeleteSelectionBtnClicked);

    // 加载对话
    loadSessions();

    // 高亮对话
    highlightSession(sessionUuid);

    ui->lineEdit_search->setFocus();
}

ChatHistoryPage::~ChatHistoryPage()
{
    delete ui;
}

void ChatHistoryPage::highlightSession(const QString &uuid)
{
    // 高亮对话控件
    QVBoxLayout *layout = (ui->stackedWidget->currentWidget() == ui->page_sessionList) ?
                              mScrollLayout : mResultLayout;

    for (int index = 0; index < layout->count(); index++)
    {
        QLayoutItem *item = layout->itemAt(index);
        QWidget *widget = item->widget();
        if (ChatSessionWgt *w = qobject_cast<ChatSessionWgt *>(widget))
        {
            if (w->uuid() == uuid)
            {
                w->setHighlight(true);
                updateSessionWgtInfo(w);
            }
            else
            {
                w->setHighlight(false);
            }
        }
    }
}

void ChatHistoryPage::showEvent(QShowEvent *event)
{
    QWidget::showEvent(event);
}

void ChatHistoryPage::hideEvent(QHideEvent *event)
{
    QWidget::hideEvent(event);
}

void ChatHistoryPage::onDeleteChatSession(const QString &uuid)
{
    // 记录数据库
    QString err;
    if (ChatDatabase::instance().deleteChatSession(uuid, err))
    {
        ChatSessionWgt *w = qobject_cast<ChatSessionWgt *>(sender());

        // 移除控件（不用判断位置，直接在所有的布局上移除）
        mScrollLayout->removeWidget(w);
        mResultLayout->removeWidget(w);

        // 删除控件
        w->deleteLater();

        updateSessionCountInfo();
        updateResultCountInfo();

        emit chatDeleted(uuid);
    }
    else
    {
        outputMessage(err, Error);
    }
}

void ChatHistoryPage::onDeleteAllBtnClicked()
{
    // 显示消息框
    QString title = "删除对话";
    QString text = QString("是否删除全部 %1 条历史对话？").arg(CHAT_DB.sessionCount());
    if (showMessageBox(title, text) == QMessageBox::Ok)
    {
        PROFILE_FUNCTION();
        if (ChatDatabase::instance().deleteAllSessions())
        {
            // 删除布局中的所有 widget
            QLayoutItem *item;
            while ((item = mScrollLayout->takeAt(0)) != nullptr) {
                if (QWidget *widget = item->widget()) {

                    widget->setParent(nullptr); // 移除父级，以便自动删除
                    widget->deleteLater();
                }
                delete item;
            }

            updateSessionCountInfo();

            // 切换回对话列表
            ui->stackedWidget->setCurrentWidget(ui->page_sessionList);

            // 隐藏信息标签
            ui->label_info->setVisible(false);
        }
        else
        {
            outputMessage(ChatDatabase::instance().lastError(), Error);
        }
    }
}

void ChatHistoryPage::onSearchSession()
{
    PROFILE_FUNCTION();

    cleanSearchResult();

    QString keyWord = ui->lineEdit_search->text();
    keyWord = keyWord.trimmed();
    if (keyWord.isEmpty())
    {
        showInfo("请输入搜索关键字");
        ui->stackedWidget->setCurrentWidget(ui->page_sessionList);
        return;
    }

    QStringList invalidKeyWords{"[", "]", "{", "}", ":", ",", "`", "\"", "null", "true", "false"};
    if (invalidKeyWords.contains(keyWord))
    {
        QString info = "关键字不能等于下列字符：";
        for (const auto &kw : invalidKeyWords)
        {
            info += kw + " ";
        }
        info.removeLast();
        showInfo(info);
        ui->stackedWidget->setCurrentWidget(ui->page_sessionList);

        return;
    }

    int totalResCount = CHAT_DB.searchResultCount(keyWord);
    if (totalResCount != -1)
    {
        QString info = QString("搜索关键字：%1，匹配到%2条结果").arg(keyWord).arg(totalResCount);
        showInfo(info);

        ui->stackedWidget->setCurrentWidget(ui->page_searchResult);
        onShowMoreResultBtnClicked();
    }
    else
    {
        showInfo(ChatDatabase::instance().lastError());
    }
}

void ChatHistoryPage::onLoadMoreBtnClicked()
{
    PROFILE_FUNCTION();
    mPageSpinner->setVisible(true);

    QList<ChatSessionBrief> brifies;
    int indexBegin = loadedSessionCount(mScrollLayout);
    int indexEnd   = indexBegin + CountPerLoading;
    if (CHAT_DB.loadSessionsByIndexRange(indexBegin, indexEnd, brifies))
    {
        for (const auto &b : brifies)
        {
            QApplication::processEvents();

            ChatSessionWgt *w = new ChatSessionWgt(b, this);
            connect(w, &ChatSessionWgt::sessionClicked, this, &ChatHistoryPage::loadSessionHistory);
            connect(w, &ChatSessionWgt::deleteBtnClicked, this, &ChatHistoryPage::onDeleteChatSession);

            // 将控件添加到按钮和弹簧的上面
            /*
            ┌────────────────────────────┐
            │             x              │
            │             x              │
            │             x              │
            │             x              │
            │             x              │
            │             x              │
            │             x              │
            │             x              │
            │             x              │
            │             x              │
            │             x              │
            │ ┌────────────────────────┐ │
            │ │      mBtnLoadMore      │ │
            │ └────────────────────────┘ │
            │ ┌────────────────────────┐ │
            │ │         Stretch        │ │
            │ └────────────────────────┘ │
            └────────────────────────────┘
             */
            mScrollLayout->insertWidget(mScrollLayout->count() - 2, w);
        }

        updateSessionCountInfo();
        updateSessionCheckMode();
    }
    else
    {
        showInfo(CHAT_DB.lastError());
    }

    mPageSpinner->setVisible(false);
}

void ChatHistoryPage::onShowMoreResultBtnClicked()
{
    PROFILE_FUNCTION();
    mPageSpinner->setVisible(true);

    int indexBegin = loadedSessionCount(mResultLayout);
    int indexEnd   = indexBegin + CountPerLoading;

    QString keyWord = QString(ui->lineEdit_search->text()).trimmed();
    QList<ChatSessionBrief> brifies;
    if (CHAT_DB.searchMessage(keyWord, indexBegin, indexEnd, brifies))
    {
        for (const auto &b : brifies)
        {
            QApplication::processEvents();

            ChatSessionWgt *w = new ChatSessionWgt(b, this);
            connect(w, &ChatSessionWgt::sessionClicked, this, &ChatHistoryPage::loadSessionHistory);
            connect(w, &ChatSessionWgt::deleteBtnClicked, this, &ChatHistoryPage::onDeleteChatSession);
            mResultLayout->insertWidget(mResultLayout->count() - 2, w);
        }

        updateResultCountInfo();
        updateSessionCheckMode();
    }
    else
    {
        showInfo(CHAT_DB.lastError());
    }

    mPageSpinner->setVisible(false);
}

void ChatHistoryPage::onStackedWidgetChanged(int index)
{
    // 切换为对话列表时清除所有搜索结果
    if (ui->stackedWidget->currentWidget() == ui->page_sessionList)
    {
        cleanSearchResult();
    }
}

void ChatHistoryPage::onCheckBtnClicked()
{
    // 设置按钮状态
    bool vis = inCheckMode();
    QString text = vis ? "取消" : "选择";
    ui->pushButton_check->setText(text);

    ui->pushButton_selectAll->setVisible(vis);
    ui->pushButton_invertSelection->setVisible(vis);
    ui->pushButton_deleteSelection->setVisible(vis);

    // 开启选项模式
    updateSessionCheckMode();
}

void ChatHistoryPage::onSelectAllBtnClicked()
{
    if (auto layout = currentLayout())
    {
        for (int index = 0; index < layout->count(); index++)
        {
            QLayoutItem *item = layout->itemAt(index);
            QWidget *widget = item->widget();
            if (auto session = qobject_cast<ChatSessionWgt *>(widget))
            {
                session->setChecked(true);
            }
        }
    }
}

void ChatHistoryPage::onInvertSelectionBtnClicked()
{
    if (auto layout = currentLayout())
    {
        for (int index = 0; index < layout->count(); index++)
        {
            QLayoutItem *item = layout->itemAt(index);
            QWidget *widget = item->widget();
            if (auto session = qobject_cast<ChatSessionWgt *>(widget))
            {
                session->setChecked(!session->isChecked());
            }
        }
    }
}

void ChatHistoryPage::onDeleteSelectionBtnClicked()
{
    // 获取选中的uuid
    QStringList selectedUuids;
    auto layout = currentLayout();
    for (int index = 0; index < layout->count(); index++)
    {
        QLayoutItem *item = layout->itemAt(index);
        QWidget *widget = item->widget();
        if (ChatSessionWgt *session = qobject_cast<ChatSessionWgt *>(widget))
        {
            if (session->isChecked())
            {
                selectedUuids << session->uuid();
            }
        }
    }

    if (selectedUuids.isEmpty())
    {
        QString title = "删除";
        QString text = QString("请勾选要删除的对话");
        showMessageBox(title, text, QMessageBox::Information);
        return;
    }

    // 显示消息框
    int count = selectedUuids.count();
    QString title = "删除对话";
    QString text = QString("是否删除 %1 条历史对话？").arg(count);
    if (showMessageBox(title, text) != QMessageBox::Ok)
        return;

    for (int index = count - 1; index >= 0; index--)
    {
        QString uuid = selectedUuids.at(index);
        QString err;
        if (!CHAT_DB.deleteChatSession(uuid, err))
        {
            outputMessage(err, Error);
            continue;
        }

        for (int ii = 0; ii < layout->count(); ii++)
        {
            if (layout->itemAt(ii) == nullptr)
                continue;

            if (ChatSessionWgt *session = qobject_cast<ChatSessionWgt *>(layout->itemAt(ii)->widget()))
            {
                if (session->uuid() != uuid) continue;

                QLayoutItem *item = layout->takeAt(ii);
                session->setParent(nullptr); // 移除父级，以便自动删除
                session->deleteLater();
                delete item;
            }
        }
    }

    // 更新按钮信息
    if (layout == mScrollLayout)
        updateSessionCountInfo();
    else
        updateResultCountInfo();
}

void ChatHistoryPage::loadSessions()
{
    onLoadMoreBtnClicked();
}

void ChatHistoryPage::updateSessionCountInfo() const
{
    int totalCount = CHAT_DB.sessionCount();
    int loadCount  = loadedSessionCount(mScrollLayout);

    QString htmlText = QString("<b style='font-size: 14pt;'>对话列表  </b> <span style='color: gray; font-size: 9pt;'>共%1组对话</span>")
                           .arg(totalCount);
    ui->label->setText(htmlText);


    bool allLoaded = (loadCount == totalCount) ? true : false;
    QString text = allLoaded ?  QString("已全部加载") : QString("加载更多");
    mBtnLoadMoreSession->setText(QString("%1  %2 / %3").arg(text).arg(loadCount).arg(totalCount));
    mBtnLoadMoreSession->setDisabled(allLoaded);
}

void ChatHistoryPage::updateSessionWgtInfo(ChatSessionWgt *wgt)
{
    ChatSession session;
    if (ChatDatabase::instance().loadSessionByUuid(wgt->uuid(), session))
    {
        ChatSessionBrief brief = session.toBrief();

        // 对话修改时间变化时更新信息
        if (brief.modifiedTime != wgt->modifiedTime())
        {
            // 更新信息
            wgt->updateInfo(brief);

            // 调整位置
            if (wgt->isVisible())
            {
                mScrollLayout->removeWidget(wgt);
                mScrollLayout->insertWidget(0, wgt);
            }
        }
    }
    else
    {
        outputMessage(ChatDatabase::instance().lastError(), Error);
    }
}

void ChatHistoryPage::updateResultCountInfo() const
{
    int totalCount = CHAT_DB.searchResultCount(QString(ui->lineEdit_search->text()).trimmed());
    int loadCount  = loadedSessionCount(mResultLayout);

    bool allLoaded = (loadCount == totalCount) ? true : false;
    QString text = allLoaded ?  QString("已全部加载") : QString("加载更多");
    mBtnLoadMoreResult->setText(QString("%1  %2 / %3").arg(text).arg(loadCount).arg(totalCount));
    mBtnLoadMoreResult->setDisabled(allLoaded);
}

void ChatHistoryPage::showInfo(const QString &info) const
{
    ui->label_info->setVisible(true);
    QString htmlText = QString("<center><span style='color: gray; font-size: 9pt;'>%1</font></center>").arg(info);
    ui->label_info->setText(htmlText);
}

void ChatHistoryPage::showSessionWidgets(bool showAll, const QStringList &uuids)
{
    for (int index = 0; index < mScrollLayout->count(); index++)
    {
        QLayoutItem *item = mScrollLayout->itemAt(index);
        QWidget *widget = item->widget();
        if (ChatSessionWgt *w = qobject_cast<ChatSessionWgt *>(widget))
        {
            if (showAll)
            {
                w->setVisible(true);
            }
            else
            {
                bool show = uuids.contains(w->uuid()) ? true : false;
                w->setVisible(show);
            }
        }
    }
}

QVBoxLayout *ChatHistoryPage::currentLayout() const
{
    if (ui->stackedWidget->currentWidget() == ui->page_sessionList)
        return mScrollLayout;
    else if (ui->stackedWidget->currentWidget() == ui->page_searchResult)
        return mResultLayout;
    else
        return nullptr;
}

int ChatHistoryPage::loadedSessionCount(QVBoxLayout *layout) const
{
    int count = 0;

    for (int index = 0; index < layout->count(); index++)
    {
        QLayoutItem *item = layout->itemAt(index);
        QWidget *widget = item->widget();
        if (qobject_cast<ChatSessionWgt *>(widget))
        {
            count++;
        }
    }

    return count;
}

void ChatHistoryPage::cleanSearchResult()
{
    // 删除布局中的所有 widget
    QLayoutItem *item;
    while ((item = mResultLayout->takeAt(0)) != nullptr) {
        if (QWidget *widget = item->widget()) {

            widget->setParent(nullptr); // 移除父级，以便自动删除
            widget->deleteLater();
        }
        delete item;
    }

    // 重新初始化按钮
    mBtnLoadMoreResult = new QPushButton(this);
    mBtnLoadMoreResult->setMinimumSize(100, 30);
    connect(mBtnLoadMoreResult, &QPushButton::clicked, this, &ChatHistoryPage::onShowMoreResultBtnClicked);

    mResultLayout->addWidget(mBtnLoadMoreResult);
    mResultLayout->insertStretch(-1);

    mBtnLoadMoreResult->setText("没有搜索结果");
    mBtnLoadMoreResult->setDisabled(false);
}

bool ChatHistoryPage::inCheckMode() const
{
    return ui->pushButton_check->isChecked();
}

void ChatHistoryPage::updateSessionCheckMode()
{
    if (auto layout = currentLayout())
    {
        for (int index = 0; index < layout->count(); index++)
        {
            QLayoutItem *item = layout->itemAt(index);
            QWidget *widget = item->widget();
            if (auto session = qobject_cast<ChatSessionWgt *>(widget))
            {
                session->setCheckMode(inCheckMode());
            }
        }
    }
}

int ChatHistoryPage::showMessageBox(const QString &title, const QString &text, QMessageBox::Icon icon)
{
    // 创建消息框
    QMessageBox *messageBox = new QMessageBox(this);
    messageBox->setWindowTitle(title);
    messageBox->setText(text);
    messageBox->setIcon(icon);

    if (icon == QMessageBox::Warning)
        messageBox->setStandardButtons(QMessageBox::Ok | QMessageBox::Cancel);

    // 获取 parentWidget 的几何信息
    QWidget *parentWidget = this->parentWidget();
    QRect parentGeometry = parentWidget->geometry();
    QPoint parentCenter = parentGeometry.center();

    // 计算消息框在 parentWidget 中间的位置
    QSize msgBoxSize = messageBox->sizeHint(); // 获取消息框的推荐尺寸
    QPoint msgBoxTopLeft = parentCenter - QPoint(msgBoxSize.width() / 2, msgBoxSize.height() / 2);
    QPoint globalTopLeft = parentWidget->mapToGlobal(msgBoxTopLeft);

    // 移动消息框到计算的位置
    messageBox->move(globalTopLeft);

    return messageBox->exec();
}


}
