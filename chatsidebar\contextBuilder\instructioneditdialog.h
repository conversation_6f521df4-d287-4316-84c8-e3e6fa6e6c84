#ifndef INSTRUCTIONEDITDIALOG_H
#define INSTRUCTIONEDITDIALOG_H

#include <QDialog>
#include "contextinstruction.h"

class QLineEdit;
class QTextEdit;

class InstructionEditDialog : public QDialog
{
    Q_OBJECT

public:
    explicit InstructionEditDialog(QWidget *parent = nullptr);
    explicit InstructionEditDialog(const ContextInstruction &instruction, QWidget *parent = nullptr);

    ContextInstruction getInstruction() const;

private:
    void setupUI();

    QLineEdit *mTitleEdit;
    QTextEdit *mContentEdit;
    ContextInstruction mInstruction;
};

#endif // INSTRUCTIONEDITDIALOG_H