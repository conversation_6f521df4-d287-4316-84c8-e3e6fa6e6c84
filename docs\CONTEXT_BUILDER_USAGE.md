# 上下文构建器使用说明

## 概述

上下文构建器是CodeBooster插件的一个重要功能，类似于16x Prompt工具，允许用户通过图形界面构建复杂的上下文信息，然后发送到AI聊天界面。

## 主要功能

### 1. 文件夹过滤选项
- **读取子文件夹开关**：控制是否递归处理子文件夹
- **忽略的文件后缀**：使用逗号分隔的文件扩展名列表（如：.env,.log,.json）
- **忽略的文件夹名**：使用逗号分隔的文件夹名列表（如：.git/,.vscode/,node_modules/）
- **自动过滤**：媒体文件、二进制文件和大于500KB的文本文件会被自动忽略

### 2. 上下文导航树
- **拖拽支持**：支持拖拽文件和文件夹到树形控件
- **工具栏操作**：
  - 删除所有：清空所有上下文项
  - 全选：选中所有上下文项
  - 全不选：取消选中所有上下文项
  - 反选：反转所有上下文项的选中状态
  - 刷新：更新文件夹结构和文件内容
- **操作列**：每个上下文项都有独立的删除按钮
- **勾选状态**：支持勾选/取消勾选上下文项
- **右键菜单**：支持删除和切换勾选状态

### 3. 指令输入框
- 输入用户的提示词指令
- 支持2秒延迟自动更新最终Prompt
- 多行文本输入

### 4. 最终Prompt框
- 只读显示最终组合的Prompt内容
- 包含上下文信息和用户指令
- 自动格式化为Markdown格式

### 5. 操作按钮
- "发送到对话框"：将选中的上下文发送到聊天界面
- "清空所有"：清除所有上下文和指令

## 使用方法

### 打开上下文构建器
1. 在聊天侧边栏的输入区域，点击工具栏中的"上下文生成器"按钮
2. 系统会切换到上下文构建器页面

### 添加上下文
1. **拖拽文件**：直接将文本文件拖拽到上下文导航树区域
2. **拖拽文件夹**：将文件夹拖拽到上下文导航树区域，系统会自动处理文件夹内的所有文本文件

### 配置过滤规则
1. **设置子文件夹选项**：选择"是"或"否"来控制是否读取子文件夹
2. **配置文件后缀过滤**：在文件后缀输入框中输入要忽略的文件扩展名，用逗号分隔
3. **配置文件夹过滤**：在文件夹输入框中输入要忽略的文件夹名，用逗号分隔
4. **使用重置按钮**：点击"重置"按钮恢复默认的过滤规则

### 输入指令
1. 在"指令输入"区域输入您的提示词
2. 系统会在停止输入2秒后自动更新最终Prompt

### 管理上下文项
1. **使用工具栏按钮**：
   - 删除所有：清空所有上下文项
   - 全选：选中所有上下文项用于发送
   - 全不选：取消选中所有上下文项
   - 反选：反转选中状态
   - 刷新：重新读取文件夹内容和文件
2. **使用操作按钮**：点击每个上下文项右侧的"删除"按钮可单独删除

### 发送到聊天
1. 确保已选中需要的上下文项（默认全部选中）
2. 点击"发送到对话框"按钮
3. 系统会自动切换回聊天页面并将上下文添加到输入框

## 技术特性

### 文件过滤
- 自动过滤非文本文件和大文件（>500KB）
- 支持逗号分隔的文件扩展名过滤
- 支持逗号分隔的文件夹名过滤
- 可选择是否包含子文件夹

### 上下文格式
- 文件上下文包含文件路径、文件名和内容
- 文件夹上下文包含文件夹路径、树形结构和所有文件内容
- 自动推断文件类型并添加语法高亮标记

### 性能优化
- 使用2秒延迟避免频繁更新
- 支持大文件夹的递归处理
- 内存友好的文件读取方式

## 默认过滤规则

### 默认忽略的文件后缀
```
.env,.log,.gitignore,.json,.o
```

### 默认忽略的文件夹
```
.git/,.svn/,.vscode/,.idea/,node_modules/,venv/
```

### 自动忽略的文件类型
- 媒体文件（图片、音频、视频）
- 二进制文件（可执行文件、库文件）
- 大于500KB的文本文件

## 注意事项

1. 只支持文本文件，二进制文件会被自动过滤
2. 大文件夹可能需要一些时间来处理
3. 过滤规则的修改会实时生效，无需手动应用
4. 上下文构建器的状态不会在会话间保持，每次打开都是空白状态
5. 使用逗号分隔多个过滤规则，空格会被自动忽略
6. 文件夹名规则建议以"/"结尾，文件扩展名规则建议以"."开头
