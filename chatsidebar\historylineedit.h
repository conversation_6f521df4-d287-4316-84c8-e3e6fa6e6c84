#ifndef HISTORYLINEEDIT_H
#define HISTORYLINEEDIT_H

#include <QLineEdit>
#include <QStringList>

/**
 * @brief The HistoryLineEdit class 一个能用方向键调整输入框显示历史的控件
 */
class HistoryLineEdit : public QLineEdit {
    Q_OBJECT

public:
    HistoryLineEdit(QWidget *parent = nullptr);

    void setHistory(QStringList value);
    void remeberCurrentText();

protected:
    void keyPressEvent(QKeyEvent *event) override;

private:
    void showHistory(int direction);

private:
    QStringList history;
    QString inputBuffer;
    int historyIndex;
};

#endif // HISTORYLINEEDIT_H
