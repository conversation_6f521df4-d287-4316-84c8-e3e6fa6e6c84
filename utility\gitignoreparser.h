#ifndef GITIGNOREPARSER_H
#define GITIGNOREPARSER_H

#include <QString>
#include <QStringList>
#include <QRegularExpression>
#include <QFileInfo>
#include <QDir>

namespace CodeBooster::Internal {

/**
 * @brief GitIgnoreParser 类用于解析 .gitignore 格式的文本并提供文件/文件夹过滤功能
 * 
 * 支持的 .gitignore 规则：
 * - 简单文件名匹配：filename.txt
 * - 通配符匹配：*.txt, *.user
 * - 目录匹配：dirname/, build/
 * - 以点开头的文件/目录：.*, .git
 * - 注释行：# 开头的行
 * - 空行：自动忽略
 */
class GitIgnoreParser
{
public:
    GitIgnoreParser();
    
    /**
     * @brief 设置忽略规则文本（.gitignore 格式）
     * @param ignoreText .gitignore 格式的文本内容
     */
    void setIgnoreRules(const QString &ignoreText);
    
    /**
     * @brief 添加单个忽略规则
     * @param rule 单个忽略规则
     */
    void addIgnoreRule(const QString &rule);
    
    /**
     * @brief 清除所有忽略规则
     */
    void clearRules();
    
    /**
     * @brief 检查文件是否应该被忽略
     * @param filePath 文件的完整路径
     * @param basePath 基础路径（用于计算相对路径）
     * @return true 如果文件应该被忽略，false 否则
     */
    bool shouldIgnoreFile(const QString &filePath, const QString &basePath = QString()) const;
    
    /**
     * @brief 检查目录是否应该被忽略
     * @param dirPath 目录的完整路径
     * @param basePath 基础路径（用于计算相对路径）
     * @return true 如果目录应该被忽略，false 否则
     */
    bool shouldIgnoreDirectory(const QString &dirPath, const QString &basePath = QString()) const;
    
    /**
     * @brief 获取默认的忽略规则
     * @return 默认的 .gitignore 格式文本
     */
    static QString getDefaultIgnoreRules();

    /**
     * @brief 获取默认忽略规则的常量引用（性能优化）
     * @return 默认规则的常量引用
     */
    static const QString& getDefaultIgnoreRulesRef();

    /**
     * @brief 从设置中加载忽略规则
     */
    void loadRulesFromSettings();

private:
    struct IgnoreRule {
        QString pattern;           // 原始模式
        QRegularExpression regex;  // 编译后的正则表达式
        bool isDirectory;          // 是否是目录规则（以 / 结尾）
        bool isNegation;          // 是否是否定规则（以 ! 开头）
        
        IgnoreRule() : isDirectory(false), isNegation(false) {}
    };
    
    QList<IgnoreRule> mIgnoreRules;
    
    /**
     * @brief 解析单个忽略规则
     * @param rule 原始规则字符串
     * @return 解析后的 IgnoreRule 对象
     */
    IgnoreRule parseRule(const QString &rule) const;
    
    /**
     * @brief 将 gitignore 模式转换为正则表达式
     * @param pattern gitignore 模式
     * @return 对应的正则表达式字符串
     */
    QString patternToRegex(const QString &pattern) const;
    
    /**
     * @brief 获取相对路径
     * @param fullPath 完整路径
     * @param basePath 基础路径
     * @return 相对路径
     */
    QString getRelativePath(const QString &fullPath, const QString &basePath) const;
    
    /**
     * @brief 检查路径是否匹配规则
     * @param path 要检查的路径（相对路径）
     * @param rule 忽略规则
     * @param isDirectory 路径是否为目录
     * @return true 如果匹配，false 否则
     */
    bool matchesRule(const QString &path, const IgnoreRule &rule, bool isDirectory) const;
};

} // namespace CodeBooster::Internal

#endif // GITIGNOREPARSER_H
