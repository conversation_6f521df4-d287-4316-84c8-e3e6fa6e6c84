#include "instructioneditdialog.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLineEdit>
#include <QTextEdit>
#include <QPushButton>
#include <QLabel>

InstructionEditDialog::InstructionEditDialog(QWidget *parent)
    : QDialog(parent)
{
    setupUI();
    setWindowTitle("新建指令");
}

InstructionEditDialog::InstructionEditDialog(const ContextInstruction &instruction, QWidget *parent)
    : QDialog(parent), mInstruction(instruction)
{
    setupUI();
    setWindowTitle("编辑指令");
    mTitleEdit->setText(mInstruction.title);
    mContentEdit->setText(mInstruction.content);
}

ContextInstruction InstructionEditDialog::getInstruction() const
{
    ContextInstruction instruction;
    instruction.title = mTitleEdit->text();
    instruction.content = mContentEdit->toPlainText();
    return instruction;
}

void InstructionEditDialog::setupUI()
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    
    QLabel *titleLabel = new QLabel("标题:", this);
    mTitleEdit = new QLineEdit(this);
    
    QLabel *contentLabel = new QLabel("内容:", this);
    mContentEdit = new QTextEdit(this);
    mContentEdit->setMinimumHeight(150);

    QHBoxLayout *buttonLayout = new QHBoxLayout();
    QPushButton *okButton = new QPushButton("确定", this);
    QPushButton *cancelButton = new QPushButton("取消", this);
    buttonLayout->addStretch();
    buttonLayout->addWidget(okButton);
    buttonLayout->addWidget(cancelButton);

    mainLayout->addWidget(titleLabel);
    mainLayout->addWidget(mTitleEdit);
    mainLayout->addWidget(contentLabel);
    mainLayout->addWidget(mContentEdit);
    mainLayout->addLayout(buttonLayout);

    connect(okButton, &QPushButton::clicked, this, &QDialog::accept);
    connect(cancelButton, &QPushButton::clicked, this, &QDialog::reject);
}