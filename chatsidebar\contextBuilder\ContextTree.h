#ifndef CONTEXTTREE_H
#define CONTEXTTREE_H

#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QAction>
#include <QToolBar>
#include <QLineEdit>
#include <QDragEnterEvent>
#include <QDragMoveEvent>
#include <QDropEvent>
#include <QMap>
#include <QSet>

#include "chatcontext/contextitem.h"

namespace CodeBooster::Internal {

class GitIgnoreParser;


/**
 * @brief 上下文导航树项，用于在树形控件中显示上下文项
 */
class ContextTreeItem : public QTreeWidgetItem
{
public:
    explicit ContextTreeItem(const ContextItem &context, QTreeWidget *parent = nullptr);
    explicit ContextTreeItem(const ContextItem &context, QTreeWidgetItem *parent = nullptr);

    ContextItem contextItem() const { return mContextItem; }
    void setContextItem(const ContextItem &context) { mContextItem = context; }

    bool isChecked() const;
    void setChecked(bool checked);

private:
    ContextItem mContextItem;
};

/**
 * @brief 支持拖拽的上下文导航树
 */
class ContextNavigationTree : public QTreeWidget
{
    Q_OBJECT

public:
    explicit ContextNavigationTree(QWidget *parent = nullptr);
    ~ContextNavigationTree();

    void addContextItem(const ContextItem &context);
    void removeContextItem(const QString &itemId);
    void clearContextItems();

    QList<ContextItem> getSelectedContextItems() const;
    QList<ContextItem> getAllContextItems() const;

    // 获取指定顶层节点的选中子文件路径
    QStringList getSelectedChildFilePaths(ContextTreeItem *topLevelItem) const;

    void setGitIgnoreRules(const QString &rules);
    void setIncludeSubfolders(bool include);

    void selectAll();
    void selectNone();
    void invertSelection();
    void refreshContextItems();
    void deselectSourceFiles();
    void addCurrentFile();
    void addCurrentFolder();

    QToolBar* getToolBar() const { return mToolBar; }
    QLineEdit* getFilterLineEdit() const { return mFilterLineEdit; }
    void setUpFilterLineEdit();

    // 拖拽事件的公共接口函数
    void handleDragEnterEvent(QDragEnterEvent *event);
    void handleDragMoveEvent(QDragMoveEvent *event);
    void handleDropEvent(QDropEvent *event);

protected:
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dragMoveEvent(QDragMoveEvent *event) override;
    void dropEvent(QDropEvent *event) override;
    //void dragLeaveEvent(QDragLeaveEvent *event) override;

private slots:
    void onItemChanged(QTreeWidgetItem *item, int column);
    void onCustomContextMenuRequested(const QPoint &pos);
    void onDeleteItemClicked();

private:
    struct RefreshStats {
        int addedFiles = 0;
        int addedFolders = 0;
        int removedFiles = 0;
        int removedFolders = 0;

        bool isEmpty() const;
        QString toString() const;
    };

    void setupContextMenu();
    void setupToolBar();
    void processDirectory(const QString &path, QString &content, int &fileCount, QString &treeStructure, const QString &prefix = "", bool isLast = true);
    void updateItemActions();
    void addDeleteButtonToItem(ContextTreeItem *item, const QString &itemId);
    void buildFolderStructure(ContextTreeItem *parentItem, const QString &folderPath);
    void updateChildrenCheckState(QTreeWidgetItem *parentItem, Qt::CheckState state);
    void updateParentCheckState(QTreeWidgetItem *childItem);

    // Refresh helpers
    void collectItemUrisAndTypes(QTreeWidgetItem *parent,
                                 QSet<QString> &uris,
                                 QMap<QString, ContextItem::ContextType> &types);
    RefreshStats
    calculateRefreshDiff(const QSet<QString> &initialUris,
                         const QMap<QString, ContextItem::ContextType> &initialTypes,
                         const QSet<QString> &finalUris,
                         const QMap<QString, ContextItem::ContextType> &finalTypes);
    void showRefreshStats(const RefreshStats &stats);

signals:
    void contextItemsChanged();

private:
    GitIgnoreParser *mGitIgnoreParser;
    bool mIncludeSubfolders;

    // 工具栏和操作
    QToolBar *mToolBar;
    QAction *mDeleteAllAction;
    QAction *mSelectAllAction;
    QAction *mSelectNoneAction;
    QAction *mInvertSelectionAction;
    QAction *mRefreshAction;
    QAction *mDeselectSourceFilesAction;
    QAction *mAddCurrentFileAction;
    QAction *mAddCurrentFolderAction;

    // 右键菜单
    QAction *mDeleteAction;
    QAction *mToggleCheckAction;

    // 过滤功能
    QLineEdit *mFilterLineEdit;

public slots:
    void onFilterTextChanged();

private:
    void filterTreeItems(const QString &filterText);
};

} // namespace CodeBooster::Internal

#endif // CONTEXTTREE_H
