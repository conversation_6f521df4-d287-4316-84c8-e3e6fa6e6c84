#ifndef CHATEXPORTDIALOG_H
#define CHATEXPORTDIALOG_H

#include <QDialog>
#include <QFutureWatcher>

#include <solutions/spinner/spinner.h>
#include "database/chatdatabase.h"

namespace Ui {
class ChatExportDialog;
}

namespace CodeBooster::Internal{

class ChatSession;

class ChatExportDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ChatExportDialog(const ChatSession &session, QWidget *parent = nullptr);
    ~ChatExportDialog();

    static QImage generateImageFromMarkdonwText(const QString &text, int width);
    static int imageScaleFactor() {return 5;}

private:
    void loadChatSession();
    QString getShowMessage(const QString &content) const;
    void refreshPreview();
    int exportImageWidth() const;

private slots:
    void onSearchLineEditTextChanged(const QString &text);
    void onSelectAllBtnClicked();
    void onDeselectAllBtnClicked();
    void onCopyBtnClicked();
    void onExportBtnClicked();
    void handleImageGenerated(const QImage &image);

private:
    Ui::ChatExportDialog *ui;

    enum DataType
    {
        RawContent = Qt::UserRole + 1
    };

    const ChatSession mSession;

    QString mText;
    QImage mImage;

    QFutureWatcher<QImage> mImageGenerationWatcher;
    SpinnerSolution::Spinner *mSpinner{nullptr};
};

}
#endif // CHATEXPORTDIALOG_H
