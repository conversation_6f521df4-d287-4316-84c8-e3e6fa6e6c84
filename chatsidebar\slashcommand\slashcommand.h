#ifndef SLASHCOMMAND_H
#define SLASHCOMMAND_H

#include "pluginsettings/codeboostersettings.h"

namespace CodeBooster::Internal{

class SlashCommand;

/**
 * @brief The SlashCommandFactory class
 */
class SlashCommandFactory
{
public:
    explicit SlashCommandFactory();
    ~SlashCommandFactory();

public:
    static SlashCommandFactory &instance();

    int reloadCommands();

    SlashCommand getCommand(const QString &name);
    QStringList commandNames() const;

private:
    QMap<QString, SlashCommand> mAllCmds; ///< QMap<name, cmd>，命令的名称为文件名，所以不会重复
};


/**
 * @brief The SlashCommand class
 */
class SlashCommand
{
public:
    explicit SlashCommand(const QString &filePath, bool &ok);
    explicit SlashCommand(){}

private:
    QString removeComments(const QString &content);
    void parseContent(const QString &content);
    void parseModelParams(const QString &modelParamsSection);
    void parseMessages(QString messagesSection);

private:
    QString mName; ///< 文件名称
    QMap<QString, QVariant> mOverrideModelParams; ///< 模型参数
    QString mDescription;
    QString mSystemMsg; ///< 系统消息
    QString mUserMsg; ///< 用户消息

};

}
#endif // SLASHCOMMAND_H
