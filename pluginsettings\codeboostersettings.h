#ifndef CODEBOOSTER_CODEBOOSTERSETTINGS_H
#define CODEBOOSTER_CODEBOOSTERSETTINGS_H

#define CB_SETTING CodeBoosterSettings::instance()

#include <utils/aspects.h>
#include <QLoggingCategory>

namespace ProjectExplorer {
class Project;
}

namespace CodeBooster {
Q_DECLARE_LOGGING_CATEGORY(codeBooster)

struct ModelParam
{
    QString title;      ///< 模型参数名称
    QString modelName;  ///< 模型名称
    QString apiUrl;    ///< 请求地址
    QString apiKey;     ///< API KEY

    // 模型参数
    double temperature = 0.1;
    double top_p       = 0.7;
    int    max_tokens  = 2048;
    int    presence_penalty = 0;
    int    frequency_penalty = 0;

    bool isValid() const
    {
        return (!apiKey.isEmpty() && !apiUrl.isEmpty() && !modelName.isEmpty());
    }

    void overrideParams(const QMap<QString, QVariant> &overrideParams)
    {
        for (auto it = overrideParams.constBegin(); it != overrideParams.constEnd(); ++it) {
            const QString& key = it.key();
            const QVariant& value = it.value();

            if (key == "temperature") {
                temperature = value.toDouble();
            } else if (key == "top_p") {
                top_p = value.toDouble();
            } else if (key == "max_tokens") {
                max_tokens = value.toInt();
            } else if (key == "presence_penalty") {
                presence_penalty = value.toInt();
            } else if (key == "frequency_penalty") {
                frequency_penalty = value.toInt();
            }
        }

        validate();
    }

    void validate()
    {
        if ((temperature < 0) || (temperature > 2.0))
        {
            temperature = 0.1;
        }

        if ((top_p < 0) || (top_p > 1))
        {
            top_p = 0.7;
        }

        if ((max_tokens < 0) || (max_tokens > 8192))
        {
            max_tokens = 2048;
        }

        if ((presence_penalty < -2.0) || (presence_penalty > 2.0))
        {
            presence_penalty = 0;
        }

        if ((frequency_penalty < -2.0) || (frequency_penalty > 2.0))
        {
            frequency_penalty = 0;
        }
    }
};

class CodeBoosterSettings : public Utils::AspectContainer
{
    Q_OBJECT
public:
    /**
     * @brief The ColorTheme enum 控件样式
     */
    enum ColorTheme
    {
        Auto, ///< 跟随QtCreator主题
        Light,///< 亮色
        Dark  ///< 暗色
    };

public:
    CodeBoosterSettings();

    /// 单一实例
    static CodeBoosterSettings &instance();

    static QJsonObject buildRequestParamJson(const ModelParam &param, bool stream);

    // 输入控件
    Utils::SelectionAspect colorScheme{this};
    Utils::BoolAspect autoComplete{this};
    Utils::StringAspect configJson{this};
    Utils::StringAspect gitIgnoreRules{this}; ///< 文件夹上下文过滤规则（.gitignore 格式）
    // end

    // 从JSON配置中读取的模型参数信息
    ModelParam acmParam() const {return mAutoCompModelParam;}
    int acmMaxContextTokens() const {return mMaxAutoCompleteContextTokens;}
    QList<ModelParam> chatParams() const { return mChatModelParams;}
    int chatAttachedMsgCount() const;
    // end

    // 从JSON配置中解析的设置选项
    // options
    bool devMode() const;
    bool predictQuestions() const {return mPredictQuestions;}
    // codeInstructions
    QStringList codeInstructions() const {return mCodeInstructions;}
    // gitIgnoreRules
    QString getGitIgnoreRules() const;
    QString getDefaultGitIgnoreRules() const;
    // end

    // 界面主题选项（Note: 暂不启用）
    ColorTheme theme();
    bool isDarkTheme();
    //


public:
    void apply()        override;

    void initConfigJsonSetting();
    bool applySucess() const;
    bool needRestart() const;

    // 自动代码补全中非模型参数且不需要开放给用户的选项设置
    bool   braceBalance()        {return true;}
    bool   expandHeaders()       {return true;}
    double prefixPercentage()    {return 0.5;}
    double maxSuffixPercentate() {return 0.5;}
    // end

signals:
    void modelConfigUpdated();
    void showModelConfigErrInfo(const QStringList &errInfos);

private:
    void parseConfigSettings(const QString &configJsonStr, QStringList &errInfos);
    void validateGitIgnoreRules(const QString &rules, QStringList &errInfos);
    QString defaultModelConfig();
    QString apiBaseToUrl(const QString &apiBase);

    static ModelParam defaultAcmModelParam();
    static ModelParam defaultChatModelParam();
    static int defaultMaxAutoCompleteContextTokens();

private:
    // 设置信息
    ColorTheme mTheme;
    ModelParam mAutoCompModelParam;     ///< 自动补全模型参数
    QList<ModelParam> mChatModelParams; ///< 对话模型参数
    int mMaxAutoCompleteContextTokens;  ///< 自动补全的最大上下文长度
    int mChatAttachedMessagesCount;     ///< 对话时附加的最大消息数量
    bool mDevMode; ///< 开发者模式
    QStringList mCodeInstructions;
    bool mPredictQuestions; ///< 侧边栏对话预测问题
    // end

    bool mApplySucess; ///< 上一次设置是否有效

    bool mNeedRestartToApply; ///< 是否需要重启以应用设置
};

class CodeBoosterProjectSettings : public Utils::AspectContainer
{
public:
    CodeBoosterProjectSettings(ProjectExplorer::Project *project, QObject *parent = nullptr);

    void save(ProjectExplorer::Project *project);
    void setUseGlobalSettings(bool useGlobal);

    bool isEnabled() const;

    Utils::BoolAspect enableAutoComplete{this};
    Utils::BoolAspect useGlobalSettings{this};
};

} // namespace CodeBooster

#endif // CODEBOOSTER_CODEBOOSTERSETTINGS_H
