# PopUp消息模块

这是一个Qt弹窗消息模块，支持多种消息类型和显示位置。

## 特性

- 支持4种消息类型：Info、Error、Correct、Warning
- 支持6种显示位置：TopLeft、TopRight、TopCenter、BottomLeft、BottomRight、Center
- 图标资源直接嵌入代码，无需外部文件依赖
- 线程安全的单例模式

## 使用方法

### CMake项目

```cmake
add_subdirectory(popupmessage)
target_link_libraries(your_target PRIVATE popupmessage)
```

### qmake项目

```pro
include($$PWD/popupmessage/popupmessage.pri)
```

### 代码示例

```cpp
#include "popupmessage/popupmessage.h"

// 显示成功消息
POPUPMSG->showMessage(this, "操作成功！", PopUpMessage::Correct);

// 显示错误消息
POPUPMSG->showMessage(this, "操作失败！", PopUpMessage::Error, PopUpMessage::TopRight);

// 显示警告消息
POPUPMSG->showMessage(this, "请注意！", PopUpMessage::Warning, PopUpMessage::Center);

// 显示信息消息
POPUPMSG->showMessage(this, "提示信息", PopUpMessage::Info, PopUpMessage::BottomLeft);
```

## 资源管理

### 图标资源

图标资源已经直接嵌入到代码中，存储在 `popup_resources.h` 文件中。如果需要更新图标：

1. 将新的PNG图标文件放入 `icons/` 文件夹
2. 运行资源生成脚本：
   ```bash
   python generate_resources.py
   ```
3. 重新编译项目

### 支持的图标文件

- `popup_correct.png` - 成功图标
- `popup_error.png` - 错误图标
- `popup_info.png` - 信息图标
- `popup_warning.png` - 警告图标

## 文件结构

```
popupmessage/
├── popupmessage.h          # 主头文件
├── popupmessage.cpp        # 主实现文件
├── popup_resources.h       # 图标资源头文件（自动生成）
├── generate_resources.py   # 资源生成脚本
├── CMakeLists.txt         # CMake配置文件
├── popupmessage.pri       # qmake配置文件
├── README.md              # 说明文档
└── icons/                 # 图标源文件夹
    ├── popup_correct.png
    ├── popup_error.png
    ├── popup_info.png
    └── popup_warning.png
```

## 迁移优势

相比之前使用QRC资源文件的版本，新版本具有以下优势：

1. **更好的模块化**：无需依赖外部资源文件，整个模块可以独立迁移
2. **简化构建**：不需要Qt的资源编译系统（AUTORCC）
3. **减少文件数量**：移除了qrc文件和相关的CMake配置
4. **运行时效率**：图标数据直接在内存中，无需通过Qt资源系统查找

## 注意事项

- `popup_resources.h` 文件是自动生成的，请勿手动编辑
- 如果修改了图标文件，需要重新运行 `generate_resources.py` 脚本
- 图标文件建议使用PNG格式，尺寸为48x48像素