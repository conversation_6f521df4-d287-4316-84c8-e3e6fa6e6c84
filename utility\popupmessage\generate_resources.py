#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图标资源生成脚本
将icons文件夹中的PNG图标转换为C++头文件中的字节数组
"""

import os
import sys
from pathlib import Path

def png_to_cpp_array(png_file_path, var_name):
    """将PNG文件转换为C++字节数组"""
    with open(png_file_path, 'rb') as f:
        data = f.read()
    
    # 生成C++数组声明
    cpp_array = f"const unsigned char {var_name}[] = {{\n"
    
    # 每行16个字节
    for i in range(0, len(data), 16):
        line = "    "
        for j in range(16):
            if i + j < len(data):
                line += f"0x{data[i + j]:02x}"
                if i + j < len(data) - 1:
                    line += ", "
        cpp_array += line + "\n"
    
    cpp_array += "};\n"
    cpp_array += f"const unsigned int {var_name}_size = {len(data)};\n\n"
    
    return cpp_array

def generate_resource_header():
    """生成资源头文件"""
    script_dir = Path(__file__).parent
    icons_dir = script_dir / "icons"
    output_file = script_dir / "popup_resources.h"
    
    if not icons_dir.exists():
        print(f"错误: icons文件夹不存在: {icons_dir}")
        return False
    
    # 图标文件映射
    icon_files = {
        "popup_correct.png": "popup_correct_data",
        "popup_error.png": "popup_error_data",
        "popup_info.png": "popup_info_data",
        "popup_warning.png": "popup_warning_data"
    }
    
    header_content = """#ifndef POPUP_RESOURCES_H
#define POPUP_RESOURCES_H

// 自动生成的图标资源文件
// 请勿手动编辑此文件

"""
    
    # 转换每个图标文件
    for filename, var_name in icon_files.items():
        icon_path = icons_dir / filename
        if icon_path.exists():
            print(f"转换 {filename} -> {var_name}")
            header_content += png_to_cpp_array(icon_path, var_name)
        else:
            print(f"警告: 图标文件不存在: {icon_path}")
    
    header_content += "#endif // POPUP_RESOURCES_H\n"
    
    # 写入头文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(header_content)
        print(f"成功生成资源头文件: {output_file}")
        return True
    except Exception as e:
        print(f"错误: 无法写入文件 {output_file}: {e}")
        return False

if __name__ == "__main__":
    print("PopUp消息图标资源生成器")
    print("=" * 40)
    
    if generate_resource_header():
        print("\n资源生成完成!")
        print("\n使用说明:")
        print("1. 在popupmessage.h中包含 #include \"popup_resources.h\"")
        print("2. 修改popupmessage.cpp中的图标加载代码")
        print("3. 删除popupmessage.qrc文件")
        print("4. 更新CMakeLists.txt和.pri文件")
    else:
        print("\n资源生成失败!")
        sys.exit(1)