#ifndef ASKSUGGESTIONWGT_H
#define ASKSUGGESTIONWGT_H

#include <QWidget>
#include <QPushButton>
#include <QLabel>

#include <solutions/spinner/spinner.h>

#include "database/chatdatabase.h"
#include "pluginsettings/codeboostersettings.h"

namespace CodeBooster {
namespace Internal {

class ClickableLabel;
class NetworkRequestManager;

class AskSuggestionWgt : public QWidget
{
    Q_OBJECT
public:
    explicit AskSuggestionWgt(QWidget *parent = nullptr);

public:
    void setInactive();
    void setActive();
    void generateSuggestions(ChatSession session, const ModelParam &param);

private slots:
    void labelClicked(QString text) ;
    void onMessageReceived(const QStringList &msgs);
    void onRequestTimeout();

private:
    void clearSuggestion();

signals:
    void suggestionCreated();
    void suggestionClicked(QString suggestionText);
    void generateTimeout();

private:
    QList<ClickableLabel *> mSuggestionLabels;
    NetworkRequestManager *mRequestMgr;

    SpinnerSolution::Spinner *mSpinner{nullptr};
};

/**
 * @brief The ClickableLabel class 可点击，具备交互效果的Label
 */
class ClickableLabel : public QLabel {
    Q_OBJECT

public:
    explicit ClickableLabel(QWidget* parent = nullptr);
    ~ClickableLabel();

    void setText(const QString &text);

signals:
    void clicked(QString text);  // 信号：鼠标左键点击时发送

protected:
    void enterEvent(QEnterEvent* event) override;  // 鼠标进入事件
    void leaveEvent(QEvent* event) override;  // 鼠标离开事件
    void mouseReleaseEvent(QMouseEvent* event) override;  // 鼠标按下事件

private:
    void setHoverStyle(bool hover);  // 设置悬浮样式

private:
    QString mStyleSheet;
    QString mHoverStyleSheet;
};


} // namespace Internal
} // namespace CodeBooster

#endif // ASKSUGGESTIONWGT_H
