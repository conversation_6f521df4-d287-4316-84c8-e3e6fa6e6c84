<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ChatExportDialog</class>
 <widget class="QDialog" name="ChatExportDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>745</width>
    <height>549</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_5">
   <item>
    <widget class="QSplitter" name="splitter">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <widget class="QGroupBox" name="groupBox">
      <property name="title">
       <string>选项</string>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="QLabel" name="label">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QComboBox" name="comboBox_model"/>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <widget class="QLineEdit" name="lineEdit_search"/>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_selectAll">
           <property name="text">
            <string>选取全部</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_deSelectAll">
           <property name="text">
            <string>清除选中</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QTreeWidget" name="treeWidget_chat">
         <column>
          <property name="text">
           <string notr="true">1</string>
          </property>
         </column>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QGroupBox" name="groupBox_2">
      <property name="title">
       <string>预览</string>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_4">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_3">
         <property name="spacing">
          <number>10</number>
         </property>
         <item>
          <widget class="QPushButton" name="pushButton_copy">
           <property name="text">
            <string>复制</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_export">
           <property name="text">
            <string>导出为文件</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="Line" name="line">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QStackedWidget" name="stackedWidget">
         <property name="currentIndex">
          <number>1</number>
         </property>
         <widget class="QWidget" name="page_text">
          <layout class="QVBoxLayout" name="verticalLayout_2">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QTextBrowser" name="textBrowser"/>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="page_image">
          <layout class="QVBoxLayout" name="verticalLayout_3" stretch="0,0">
           <property name="spacing">
            <number>6</number>
           </property>
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="0,0,0,2">
             <property name="spacing">
              <number>10</number>
             </property>
             <item>
              <widget class="QLabel" name="label_2">
               <property name="text">
                <string>图片宽度：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QDoubleSpinBox" name="spinBox_imageWidth"/>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_refreshImage">
               <property name="text">
                <string>重新生成</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_2">
               <property name="orientation">
                <enum>Qt::Orientation::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QScrollArea" name="scrollArea">
             <property name="widgetResizable">
              <bool>true</bool>
             </property>
             <widget class="QWidget" name="scrollAreaWidgetContents">
              <property name="geometry">
               <rect>
                <x>0</x>
                <y>0</y>
                <width>334</width>
                <height>414</height>
               </rect>
              </property>
              <layout class="QVBoxLayout" name="verticalLayout_6">
               <property name="spacing">
                <number>0</number>
               </property>
               <property name="leftMargin">
                <number>0</number>
               </property>
               <property name="topMargin">
                <number>0</number>
               </property>
               <property name="rightMargin">
                <number>0</number>
               </property>
               <property name="bottomMargin">
                <number>0</number>
               </property>
               <item>
                <widget class="QLabel" name="label_image">
                 <property name="text">
                  <string/>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </widget>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
