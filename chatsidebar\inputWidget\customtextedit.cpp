#include "customtextedit.h"

#include <QTextBlock>
#include <QDebug>
#include <QMimeData>
#include <QTimer>
#include <QDragEnterEvent>
#include <QDragMoveEvent>
#include <QDropEvent>
#include <QPainter>
#include <QScrollBar>
#include <QDir>
#include <QFileInfo>

#include <texteditor/texteditor.h>
#include <utils/stylehelper.h>

#include "common/widgettheme.h"
#include "pluginsettings/codeboostersettings.h"
#include "common/codeboosterutils.h"
#include "utility/gitignoreparser.h"

using namespace TextEditor;

namespace CodeBooster::Internal{

/********************************************************************
 CustomTextEdit
 自定义输入控件
*********************************************************************/
CustomTextEdit::CustomTextEdit(QWidget *parent) :
    QPlainTextEdit(parent)
    , mFocusMode(false)
{
    // 初始化配色方案
    {
        if (CB_SETTING.isDarkTheme())
        {
            mColorScheme.highlightLineBackground = QColor("#ff764d");
            mColorScheme.lineNumberAreaBackground = QColor("#222222");
            mColorScheme.lineNumberText = QColor("#666666");
            mColorScheme.topSeparator = QColor("#222222");
        }
        else
        {
            mColorScheme.highlightLineBackground = QColor("#ff764d");
            mColorScheme.lineNumberAreaBackground = QColor("#e0e0e0");
            mColorScheme.lineNumberText = QColor("#909090");
            mColorScheme.topSeparator = QColor("#e0e0e0");

            QPalette palette = this->palette();
            palette.setColor(QPalette::PlaceholderText, QColor("#909090"));
            this->setPalette(palette);
        }

        mColorScheme.highlightLineBackground.setAlpha(70);
    }

    // 初始化输入框高度相关设置
    {
        mMinInputHeight = 52;
        mMaxInputHeight = 400;

        setMaximumHeight(mMinInputHeight);
        connect(this, &CustomTextEdit::textChanged, this, &CustomTextEdit::adjustInputEditSize);
        connect(this, &CustomTextEdit::sizeChanged, this, &CustomTextEdit::adjustInputEditSize);
    }

    // 初始化行号区域控件
    {
        lineNumberArea = new LineNumberArea(this);

        connect(this, SIGNAL(blockCountChanged(int)), this, SLOT(updateLineNumberAreaWidth(int)));
        connect(this, SIGNAL(updateRequest(QRect,int)), this, SLOT(updateLineNumberArea(QRect,int)));
        connect(this, &CustomTextEdit::cursorPositionChanged, this, [this](){
            highlightCurrentLine(mFocusMode);
        });

        // 默认不开启专注模式
        setFocusMode(false);
    }

    // 文本输入框不显示边框
    setStyleSheet(QString("QPlainTextEdit { border: none; background-color: %1; }").arg(CB_THEME.Color_InputTextBackground));

    // 初始化 GitIgnore 解析器
    {
        mGitIgnoreParser = new CodeBooster::Internal::GitIgnoreParser();
    }

    // 设置占位文本
    setPlaceholderTextVisible(true);
}

CustomTextEdit::~CustomTextEdit()
{
    delete mGitIgnoreParser;
}

void CustomTextEdit::setFocusMode(bool enable)
{
    mFocusMode = enable;

    lineNumberArea->setVisible(enable);
    highlightCurrentLine(enable);
    updateLineNumberAreaWidth();

    mMaxInputHeight = enable ? 1000 : 400;
    adjustInputEditSize();

    setPlaceholderTextVisible(true);
}

void CustomTextEdit::lineNumberAreaPaintEvent(QPaintEvent *event)
{
    QPainter painter(lineNumberArea);
    painter.fillRect(event->rect(), mColorScheme.lineNumberAreaBackground);

    // 绘制横线，横跨行号区域和编辑器区域
    if (mFocusMode)
    {
        QPen pen(mColorScheme.topSeparator);
        pen.setWidth(6);
        painter.setPen(pen);
        painter.drawLine(0, 0, width(), 0);
    }

    QTextBlock block = firstVisibleBlock();
    int blockNumber = block.blockNumber();
    int top = (int) blockBoundingGeometry(block).translated(contentOffset()).top();
    int bottom = top + (int) blockBoundingRect(block).height();

    QTextCursor cursor = textCursor();
    int currentBlockNumber = cursor.blockNumber();

    while (block.isValid() && top <= event->rect().bottom()) {
        if (block.isVisible() && bottom >= event->rect().top()) {
            QString number = QString::number(blockNumber + 1);
            painter.setPen(mColorScheme.lineNumberText);
            painter.drawText(0, top, lineNumberArea->width(), fontMetrics().height(),
                             Qt::AlignRight, number);

            // 在当前行号区域绘制高亮行颜色
            if (blockNumber == currentBlockNumber)
            {
                painter.fillRect(0, top, lineNumberArea->width(), fontMetrics().height() + 1, mColorScheme.highlightLineBackground);
            }
        }

        block = block.next();
        top = bottom;
        bottom = top + (int) blockBoundingRect(block).height();
        ++blockNumber;
    }
}

int CustomTextEdit::lineNumberAreaWidth()
{
    int digits = 1;
    int max = qMax(1, blockCount());
    while (max >= 10) {
        max /= 10;
        ++digits;
    }

    int space = 3 + fontMetrics().horizontalAdvance(QLatin1Char('9')) * digits;

    return space;
}

void CustomTextEdit::removeContextTag(const ContextItem &context)
{
    QString text = toPlainText();
    QString simpleTag = QString(context.tagText()).trimmed();
    text.remove(simpleTag);
    text.remove(context.tagText());

    setPlainText(text);
}

bool CustomTextEdit::event(QEvent *event)
{
    if (event->type() == QEvent::ShortcutOverride)
    {
        QKeyEvent *ke = static_cast<QKeyEvent *>(event);
        if (ke->modifiers().testFlag(Qt::ControlModifier))
        {
            if (ke->key() == Qt::Key_N)
            {
                emit newChat();

                event->accept();
                return true;
            }
        }
    }

    return QPlainTextEdit::event(event);
}

void CustomTextEdit::paintEvent(QPaintEvent *event)
{
    QPlainTextEdit::paintEvent(event);

    QPainter painter(viewport());
    painter.setPen(Qt::black);

    // 获取行号区域的宽度
    int lineNumberAreaWidth = this->lineNumberAreaWidth();

    // 绘制横线，横跨行号区域和编辑器区域
    if (mFocusMode)
    {
        QPen pen(mColorScheme.topSeparator);
        pen.setWidth(6);
        painter.setPen(pen);
        painter.drawLine(0, 0, width(), 0);

        QTextCursor cursor = textCursor();
        QTextBlock block = cursor.block();
        int top = (int) blockBoundingGeometry(block).translated(contentOffset()).top();

        // 在行号和当前行高亮区域之间绘制高亮颜色
        painter.fillRect(0, top, 4, fontMetrics().height() + 1, mColorScheme.highlightLineBackground);
    }
}

void CustomTextEdit::resizeEvent(QResizeEvent *event)
{
    QPlainTextEdit::resizeEvent(event);

    QRect cr = contentsRect();
    lineNumberArea->setGeometry(QRect(cr.left(), cr.top(), lineNumberAreaWidth(), cr.height()));
}

void CustomTextEdit::focusInEvent(QFocusEvent *event)
{
    QPlainTextEdit::focusInEvent(event);
    emit focusChange(true);
}

void CustomTextEdit::focusOutEvent(QFocusEvent *event)
{
    QPlainTextEdit::focusOutEvent(event);
    emit focusChange(false);
}

void CustomTextEdit::keyPressEvent(QKeyEvent *event)
{
    if (event->key() == Qt::Key_Return)
    {
        if (mFocusMode)
        {
            if (event->modifiers() == Qt::NoModifier)
            {
                insertPlainText("\n");
            }
            else if (event->modifiers() == Qt::ControlModifier)
            {
                emit sendMessage();
            }
        }
        else
        {
            if (event->modifiers() == Qt::ShiftModifier)
            {
                // 处理 Shift+Enter 组合键
                insertPlainText("\n");
            }
            else if (event->modifiers() == Qt::NoModifier)
            {
                emit sendMessage();
            }
        }

        return;
    }
    else if (event->key() == Qt::Key_F)
    {
        if (event->modifiers() == Qt::ControlModifier)
        {
            emit focusModeShortcutPress();
            return;
        }
    }

    // 调用基类的 keyPressEvent 处理其他按键事件
    QPlainTextEdit::keyPressEvent(event);
}

void CustomTextEdit::dragEnterEvent(QDragEnterEvent *event)
{
    // 检查拖拽数据是否包含URL
    if (event->mimeData()->hasUrls())
    {
        QList<QUrl> urlList = event->mimeData()->urls();
        bool hasValidFiles = false;

        for (const QUrl &url : urlList)
        {
            if (!url.isLocalFile())
                continue;

            QString path = url.toLocalFile();
            QFileInfo fileInfo(path);

            if (fileInfo.exists())
            {
                // 如果是文件夹，接受拖拽（文件夹内的非文本文件会在处理时被跳过）
                if (fileInfo.isDir())
                {
                    hasValidFiles = true;
                    continue;
                }

                // 如果是文件，检查是否为文本文件
                if (fileInfo.isFile())
                {
                    if (fileIsTextFile(path))
                    {
                        hasValidFiles = true;
                    }
                    else
                    {
                        // 发现非文本文件，拒绝整个拖拽操作
                        event->ignore();
                        return;
                    }
                }
            }
        }

        if (hasValidFiles)
        {
            event->acceptProposedAction();
        }
        else
        {
            event->ignore();
        }
    }
    else
    {
        // 如果不是URL数据，调用基类处理
        QPlainTextEdit::dragEnterEvent(event);
    }
}

void CustomTextEdit::dragMoveEvent(QDragMoveEvent *event)
{
    // 检查拖拽数据是否包含URL
    if (event->mimeData()->hasUrls())
    {
        QList<QUrl> urlList = event->mimeData()->urls();
        bool hasValidFiles = false;

        for (const QUrl &url : urlList)
        {
            if (!url.isLocalFile())
                continue;

            QString path = url.toLocalFile();
            QFileInfo fileInfo(path);

            if (fileInfo.exists())
            {
                // 如果是文件夹，接受拖拽
                if (fileInfo.isDir())
                {
                    hasValidFiles = true;
                    continue;
                }

                // 如果是文件，检查是否为文本文件
                if (fileInfo.isFile())
                {
                    if (fileIsTextFile(path))
                    {
                        hasValidFiles = true;
                    }
                    else
                    {
                        // 发现非文本文件，拒绝拖拽
                        event->ignore();
                        return;
                    }
                }
            }
        }

        if (hasValidFiles)
        {
            event->acceptProposedAction();
        }
        else
        {
            event->ignore();
        }
    }
    else
    {
        // 如果不是URL数据，调用基类处理
        QPlainTextEdit::dragMoveEvent(event);
    }
}

void CustomTextEdit::dropEvent(QDropEvent *event)
{
    // 获取拖拽进来的数据
    const QMimeData *mimeData = event->mimeData();

    // 如果拖拽的数据是URL格式，先检查是否包含非文本文件
    if (mimeData->hasUrls())
    {
        QList<QUrl> urlList = mimeData->urls();
        for (const QUrl &url : urlList)
        {
            if (!url.isLocalFile())
                continue;

            QString path = url.toLocalFile();
            QFileInfo fileInfo(path);

            if (fileInfo.exists() && fileInfo.isFile())
            {
                // 检查文件是否为文本文件，如果不是则拒绝整个拖拽操作
                if (!fileIsTextFile(path))
                {
                    // 拒绝拖拽事件
                    event->ignore();
                    return;
                }
            }
        }
    }

    // 解决拖拽后光标不刷新的问题：https://www.qtcentre.org/threads/16935-Cursor-stops-being-redrawn-when-QTextEdit-dropEvent()-overrided
    this->setReadOnly(true);
    QPlainTextEdit::dropEvent(event);
    this->setReadOnly(false);

    // 获取拖放的位置
    QPoint dropPosition = event->pos();
    // 将拖放位置转换为文档中的位置
    QTextCursor cursor = cursorForPosition(dropPosition);
    this->setTextCursor(cursor);

    // 处理拖拽的URL数据
    if (mimeData->hasUrls())
    {
        QList<QUrl> urlList = mimeData->urls();
        for (const QUrl &url : urlList)
        {
            if (!url.isLocalFile())
                continue;

            QString path = url.toLocalFile();
            QFileInfo fileInfo(path);

            if (fileInfo.exists())
            {
                // 拖进来文件
                if (fileInfo.isFile())
                {
                    // 此时已经确认是文本文件，直接处理
                    bool success = true;
                    QString content = readTextFile(path, success);
                    if (!success)
                    {
                        // todo: 输出日志
                        continue;
                    }

                    ContextItem item;
                    item.name = fileInfo.fileName();
                    item.description = fileInfo.path();
                    item.content = content;
                    item.type = ContextItem::File;
                    item.uri = path;

                    addContextItem(item);
                }
                // 拖进来文件夹
                else if (fileInfo.isDir())
                {
                    QString content;
                    int fileCount = 0;
                    QString treeStructure;

                    // 添加根目录到树形结构，使用点作为根标识
                    treeStructure += ".\n";
                    treeStructure += "└── " + fileInfo.fileName() + "/\n";

                    processDirectory(path, content, fileCount, treeStructure, "    "); // 调用递归处理函数

                    ContextItem item;
                    item.name = fileInfo.fileName();
                    item.description = fileInfo.path();
                    item.content = content;
                    item.type = ContextItem::Folder;
                    item.uri = path;
                    item.fileCount = fileCount; // 设置文件数量
                    item.treeStructure = treeStructure; // 设置树形结构

                    addContextItem(item);
                }
            }
        }
    }

    // 接受拖拽事件
    event->accept();
}

void CustomTextEdit::setPlaceholderTextVisible(bool visible)
{
    if (!visible)
    {
        setPlaceholderText(QString());
        return;
    }

    QString text;
    if (mFocusMode)
    {
        text = QString("Ctrl+Enter 发送，Enter 换行，Ctrl+N 创建新对话，Ctrl+F 退出专注模式");
    }
    else
    {
        text = QString("Enter 发送，Shift+Enter 换行，Ctrl+N 创建新对话，Ctrl+F 进入专注模式");
    }

    setPlaceholderText(text);
}

void CustomTextEdit::addContextItem(const ContextItem &item)
{
    this->insertPlainText(item.tagText());
    emit newContextItem(item);
}

// 递归处理文件夹的函数
void CustomTextEdit::processDirectory(const QString &path, QString &content, int &fileCount, QString &treeStructure, const QString &prefix, bool isLast)
{
    QDir dir(path);
    QStringList entryList = dir.entryList(QDir::Files | QDir::Dirs | QDir::NoDotAndDotDot);

    // 分离文件和目录，并排序
    QStringList files, directories;
    for (const QString &entry : entryList) {
        QString fullPath = dir.filePath(entry);
        QFileInfo fileInfo(fullPath);

        if (fileInfo.isFile()) {
            // 检查文件是否应该被忽略
            if (mGitIgnoreParser && mGitIgnoreParser->shouldIgnoreFile(fullPath, path)) {
                devLog(QString("[GitIgnore] 忽略文件: %1").arg(fullPath));
                continue;
            }
            if (!fileIsTextFile(fullPath)) {
                continue;
            }
            files.append(entry);
        } else if (fileInfo.isDir()) {
            // 检查目录是否应该被忽略
            if (mGitIgnoreParser && mGitIgnoreParser->shouldIgnoreDirectory(fullPath, path)) {
                devLog(QString("[GitIgnore] 忽略目录: %1").arg(fullPath));
                continue;
            }
            directories.append(entry);
        }
    }

    // 排序
    files.sort();
    directories.sort();

    // 合并列表，目录在前，文件在后
    QStringList allEntries = directories + files;

    for (int i = 0; i < allEntries.size(); ++i) {
        const QString &entry = allEntries[i];
        QString fullPath = dir.filePath(entry);
        QFileInfo fileInfo(fullPath);
        bool isLastEntry = (i == allEntries.size() - 1);

        // 构建树形结构的当前行
        QString currentPrefix = prefix;
        if (isLastEntry) {
            currentPrefix += "└── ";
        } else {
            currentPrefix += "├── ";
        }

        if (fileInfo.isFile()) {
            // 添加文件到树形结构
            treeStructure += currentPrefix + entry + "\n";

            // 处理文件内容
            bool success = true;
            QString fileContent = readTextFile(fullPath, success);
            if (!success) {
                qDebug() << "Failed to read file:" << fullPath;
                continue;
            }

            content += fileContent + "\n\n";
            fileCount++; // 增加文件计数

        } else if (fileInfo.isDir()) {
            // 添加目录到树形结构
            treeStructure += currentPrefix + entry + "/\n";

            // 为子目录准备前缀
            QString nextPrefix = prefix;
            if (isLastEntry) {
                nextPrefix += "    "; // 4个空格
            } else {
                nextPrefix += "│   "; // 竖线 + 3个空格
            }

            // 递归处理子目录
            processDirectory(fullPath, content, fileCount, treeStructure, nextPrefix, isLastEntry);
        }
    }
}

void CustomTextEdit::adjustInputEditSize()
{
    int oldHeight = height();
    int newHeight = oldHeight;

    if (mFocusMode)
    {
        setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

        // 防止重复触发此槽函数
        blockSignals(true);
        setMaximumHeight(1000);
        blockSignals(false);
    }
    else
    {
        QTextDocument *doc = document();
        QFontMetrics fm(font());
        int contentHeight = doc->size().height() * fm.lineSpacing();

        // 计算实际显示的行数
        int lineCount = 0;
        for (QTextBlock block = doc->begin(); block != doc->end(); block = block.next()) {
            QTextLayout *layout = block.layout();
            lineCount += layout->lineCount();
        }
        // 当文本行数大于1行时,为了防止文本末尾的空行导致显示进度条，将文本内容高度加上一个文本高度，
        // 将空行显示在输入控件内部，禁止进度条的显示
        if (lineCount >= 2)
        {
            contentHeight += fm.height();
        }

        // 控件高度固定比文本高度高一定的高度，数值是手动调整到合适的高度得到的
        int widgetHeight = contentHeight + 12;

        newHeight = qMin(widgetHeight, mMaxInputHeight);
        newHeight = qMax(mMinInputHeight,newHeight );

        if (newHeight <= mMinInputHeight) {
            setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
        } else {
            setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        }

        // 防止重复触发此槽函数
        blockSignals(true);
        setFixedHeight(newHeight);
        blockSignals(false);
    }

    if (newHeight != oldHeight)
        emit heightChanged(newHeight);
}

void CustomTextEdit::updateLineNumberAreaWidth(int newBlockCount)
{
    Q_UNUSED(newBlockCount)

    int width = mFocusMode ? lineNumberAreaWidth() : 0;
    setViewportMargins(width, 0, 0, 0);
}

void CustomTextEdit::highlightCurrentLine(bool highlight)
{
    QList<QTextEdit::ExtraSelection> extraSelections;

    if (highlight && !isReadOnly()) {
        QTextEdit::ExtraSelection selection;

        selection.format.setBackground(mColorScheme.highlightLineBackground);
        selection.format.setProperty(QTextFormat::FullWidthSelection, true);
        selection.cursor = textCursor();
        selection.cursor.clearSelection();
        extraSelections.append(selection);
    }

    setExtraSelections(extraSelections);
}

void CustomTextEdit::updateLineNumberArea(const QRect &rect, int dy)
{
    if (dy)
        lineNumberArea->scroll(0, dy);
    else
        lineNumberArea->update(0, rect.y(), lineNumberArea->width(), rect.height());

    if (rect.contains(viewport()->rect()))
        updateLineNumberAreaWidth(0);
}


// -------------------------------------------------------------------------
// LineNumberArea
// -------------------------------------------------------------------------
LineNumberArea::LineNumberArea(CustomTextEdit *editor) : QWidget(editor) {
    codeEditor = editor;
}

QSize LineNumberArea::sizeHint() const  {
    return QSize(codeEditor->lineNumberAreaWidth(), 0);
}

void LineNumberArea::paintEvent(QPaintEvent *event) {
    codeEditor->lineNumberAreaPaintEvent(event);
}

}
