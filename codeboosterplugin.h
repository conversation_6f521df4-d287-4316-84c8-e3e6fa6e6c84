#pragma once

#include "codeboosterclient.h"

#include <extensionsystem/iplugin.h>
#include <QPointer>
#include <solutions/spinner/spinner.h>

#include "askcodeboostertaskhandler.h"

namespace CodeBooster {
namespace Internal {

class CodeBoosterPlugin : public ExtensionSystem::IPlugin
{
    Q_OBJECT
    Q_PLUGIN_METADATA(IID "org.qt-project.Qt.QtCreatorPlugin" FILE "CodeBooster.json")

public:
    static CodeBoosterPlugin *instance();

public:
    void initialize() override;
    void extensionsInitialized() override;
    void restartClient();
    ShutdownFlag aboutToShutdown() override;

private slots:
    void onHandleAskCodeBossterTask(const QString &sysMsg, const QString &userMsg, const QList<ContextItem> &contexts);

signals:
    void documentSelectionChanged(const QString &fileName, const QString &text, int startLine, int endLine);
    void askCompileError(const QString &sysMsg, const QString &userMsg);

    void completeReplyStateChanged(bool running);

private:
    QPointer<CodeBoosterClient> m_client;
    AskCodeBoosterTaskHandler mAskCompileErrorHandler;

    SpinnerSolution::Spinner *mSpinner{nullptr};
};

} // namespace Internal
} // namespace CodeBooster
