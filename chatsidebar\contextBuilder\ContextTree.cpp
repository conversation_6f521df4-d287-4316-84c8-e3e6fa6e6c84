#include "ContextTree.h"

// #include <Core/idocument.h>
#include <coreplugin/editormanager/editormanager.h>

#include <QHeaderView>
#include <QMenu>
#include <QToolButton>
#include <QFileInfo>
#include <QDir>
#include <QUuid>
#include <functional>
#include <QMimeData>
#include <QMap>
#include <QPair>
#include <QTimer>
#include <QSet>

#include "common/codeboostericons.h"
#include "common/codeboosterutils.h"
#include "utility/gitignoreparser.h"
#include "utility/popupmessage/popupmessage.h"

namespace CodeBooster::Internal {

// -------------------------------------------------------------------------
// ContextTreeItem 实现
// -------------------------------------------------------------------------

ContextTreeItem::ContextTreeItem(const ContextItem &context, QTreeWidget *parent)
    : QTreeWidgetItem(parent), mContextItem(context)
{
    setText(0, context.name);
    setIcon(0, context.icon());
    setCheckState(0, Qt::Checked);
    setToolTip(0, context.description);

    // 如果是文件夹类型，显示树形结构
    if (context.type == ContextItem::Folder && !context.treeStructure.isEmpty()) {
        setToolTip(0, context.treeStructure);
    }
}

ContextTreeItem::ContextTreeItem(const ContextItem &context, QTreeWidgetItem *parent)
    : QTreeWidgetItem(parent), mContextItem(context)
{
    setText(0, context.name);
    setIcon(0, context.icon());
    setCheckState(0, Qt::Checked);
    setToolTip(0, context.description);

    // 如果是文件夹类型，显示树形结构
    if (context.type == ContextItem::Folder && !context.treeStructure.isEmpty()) {
        setToolTip(0, context.treeStructure);
    }
}

bool ContextTreeItem::isChecked() const
{
    return checkState(0) != Qt::Unchecked;
}

void ContextTreeItem::setChecked(bool checked)
{
    setCheckState(0, checked ? Qt::Checked : Qt::Unchecked);
}

// -------------------------------------------------------------------------
// ContextNavigationTree 实现
// -------------------------------------------------------------------------

ContextNavigationTree::ContextNavigationTree(QWidget *parent)
    : QTreeWidget(parent), mGitIgnoreParser(nullptr), mIncludeSubfolders(true), mFilterLineEdit(nullptr)
{
    // 设置基本属性
    setHeaderLabels(QStringList() << "项目" << "操作");
    setRootIsDecorated(true);
    setAlternatingRowColors(true);
    setSelectionMode(QAbstractItemView::ExtendedSelection);
    setDragDropMode(QAbstractItemView::DropOnly);
    setAcceptDrops(true);

    // 设置列宽
    header()->setStretchLastSection(false);
    header()->setSectionResizeMode(0, QHeaderView::Stretch);
    header()->setSectionResizeMode(1, QHeaderView::ResizeToContents);

    // 启用右键菜单
    setContextMenuPolicy(Qt::CustomContextMenu);
    setupContextMenu();
    setupToolBar();

    // 初始化 GitIgnore 解析器
    mGitIgnoreParser = new GitIgnoreParser();

    // 连接信号
    connect(this, &QTreeWidget::itemChanged, this, &ContextNavigationTree::onItemChanged);
    connect(this, &QTreeWidget::customContextMenuRequested, this, &ContextNavigationTree::onCustomContextMenuRequested);
}

ContextNavigationTree::~ContextNavigationTree()
{
    delete mGitIgnoreParser;
}

void ContextNavigationTree::setupContextMenu()
{
    mDeleteAction = new QAction("删除", this);
    mDeleteAction->setIcon(QIcon(":/icons/delete.png"));
    connect(mDeleteAction, &QAction::triggered, this, [this]() {
        QList<QTreeWidgetItem*> selectedItems = this->selectedItems();
        for (QTreeWidgetItem *item : selectedItems) {
            delete item;
        }
        emit contextItemsChanged();
    });

    mToggleCheckAction = new QAction("切换勾选状态", this);
    connect(mToggleCheckAction, &QAction::triggered, this, [this]() {
        QList<QTreeWidgetItem*> selectedItems = this->selectedItems();
        for (QTreeWidgetItem *item : selectedItems) {
            ContextTreeItem *contextItem = dynamic_cast<ContextTreeItem*>(item);
            if (contextItem) {
                contextItem->setChecked(!contextItem->isChecked());
            }
        }
        emit contextItemsChanged();
    });
}

void ContextNavigationTree::setupToolBar()
{
    mToolBar = new QToolBar(this);
    // mToolBar->setToolButtonStyle(Qt::ToolButtonTextOnly);
    mToolBar->setStyleSheet("QToolBar { border: none; background: transparent; }");

    // 添加水平弹簧
    QWidget *spacer = new QWidget();
    spacer->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
    mToolBar->addWidget(spacer);

    // 删除所有
    mDeleteAllAction = new QAction(ICON_REMOVEALL.icon(), "删除所有", this);
    connect(mDeleteAllAction, &QAction::triggered, this, &ContextNavigationTree::clearContextItems);
    mToolBar->addAction(mDeleteAllAction);

    // 添加当前文件
    mAddCurrentFileAction = new QAction(ICON_ADDFILE.icon(), "添加当前文件", this);
    connect(mAddCurrentFileAction, &QAction::triggered, this, &ContextNavigationTree::addCurrentFile);
    mToolBar->addAction(mAddCurrentFileAction);

    // 添加当前文件夹
    mAddCurrentFolderAction = new QAction(ICON_ADDCONTEXT.icon(), "添加当前文件夹", this);
    connect(mAddCurrentFolderAction, &QAction::triggered, this, &ContextNavigationTree::addCurrentFolder);
    mToolBar->addAction(mAddCurrentFolderAction);

    mToolBar->addSeparator();

    // 全选
    mSelectAllAction = new QAction(ICON_SELECTALL.icon(), "全选", this);
    connect(mSelectAllAction, &QAction::triggered, this, &ContextNavigationTree::selectAll);
    mToolBar->addAction(mSelectAllAction);

    // 全不选
    mSelectNoneAction = new QAction(ICON_UNSELECT.icon(), "全不选", this);
    connect(mSelectNoneAction, &QAction::triggered, this, &ContextNavigationTree::selectNone);
    mToolBar->addAction(mSelectNoneAction);

    // 反选
    mInvertSelectionAction = new QAction(ICON_DESELECT.icon(), "反选", this);
    connect(mInvertSelectionAction, &QAction::triggered, this, &ContextNavigationTree::invertSelection);
    mToolBar->addAction(mInvertSelectionAction);

    // 不选择源文件
    mDeselectSourceFilesAction = new QAction(ICON_REMOVESOURCEFILE.icon(), "不选择源文件", this);
    connect(mDeselectSourceFilesAction, &QAction::triggered, this, &ContextNavigationTree::deselectSourceFiles);
    mToolBar->addAction(mDeselectSourceFilesAction);

    mToolBar->addSeparator();


    // 刷新
    mRefreshAction = new QAction(ICON_REFRESH.icon(), "刷新", this);
    connect(mRefreshAction, &QAction::triggered, this, &ContextNavigationTree::refreshContextItems);
    mToolBar->addAction(mRefreshAction);

    updateItemActions();
}

void ContextNavigationTree::setUpFilterLineEdit()
{
    // 创建过滤输入框
    mFilterLineEdit = new QLineEdit(this);
    mFilterLineEdit->setPlaceholderText("输入关键字过滤文件和文件夹");
    // mFilterLineEdit->setMaximumWidth(150);
    // mFilterLineEdit->setMinimumWidth(120);
    connect(mFilterLineEdit, &QLineEdit::textChanged, this, &ContextNavigationTree::onFilterTextChanged);
}

void ContextNavigationTree::addContextItem(const ContextItem &context)
{
    // 检查是否已存在相同的项
    for (int i = 0; i < topLevelItemCount(); ++i) {
        ContextTreeItem *existingItem = dynamic_cast<ContextTreeItem*>(topLevelItem(i));
        if (existingItem && existingItem->contextItem().itemId == context.itemId) {
            // 已存在，更新内容
            existingItem->setContextItem(context);
            existingItem->setText(0, context.name);
            existingItem->setIcon(0, context.icon());
            existingItem->setToolTip(0, context.description);

            // 如果是文件夹且启用了子文件夹，重新构建子项
            if (context.type == ContextItem::Folder && mIncludeSubfolders) {
                // 清除现有子项
                existingItem->takeChildren();
                // 重新添加子项
                buildFolderStructure(existingItem, context.uri);
            }

            updateItemActions();
            emit contextItemsChanged();
            return;
        }
    }

    // 不存在，添加新项
    ContextTreeItem *newItem = new ContextTreeItem(context, this);
    addTopLevelItem(newItem);

    // 为顶层项添加删除按钮
    addDeleteButtonToItem(newItem, context.itemId);

    // 如果是文件夹且启用了子文件夹，构建子项结构
    if (context.type == ContextItem::Folder && mIncludeSubfolders) {
        buildFolderStructure(newItem, context.uri);
        newItem->setExpanded(true); // 默认展开
    }

    updateItemActions();
    emit contextItemsChanged();
}

void ContextNavigationTree::addCurrentFile()
{
    using namespace Core;

    if (const IDocument *document = EditorManager::currentDocument())
    {
        bool ok = true;
        ContextItem item = ContextItem::buildFileContextFromFilePath(document->filePath(), ok);

        if (ok)
        {
            addContextItem(item);
        }
    }
}

void ContextNavigationTree::addCurrentFolder()
{
    using namespace Core;

    if (const IDocument *document = Core::EditorManager::currentDocument()) {
        QFileInfo fileInfo(document->filePath().absoluteFilePath().path());
        QString folderPath = fileInfo.absolutePath();

        bool success = false;
        ContextItem item = ContextItem::buildFolderContextFromPath(Utils::FilePath::fromString(folderPath),
                                                                    success);
        if (success) {
            addContextItem(item);
        }
    }
}

void ContextNavigationTree::removeContextItem(const QString &itemId)
{
    // 递归查找并删除指定itemId的项目
    std::function<bool(QTreeWidgetItem*)> findAndRemove = [&](QTreeWidgetItem* parentItem) -> bool {
        if (!parentItem) {
            // 处理顶层项目
            for (int i = 0; i < topLevelItemCount(); ++i) {
                ContextTreeItem *item = dynamic_cast<ContextTreeItem*>(topLevelItem(i));
                if (item && item->contextItem().itemId == itemId) {
                    delete takeTopLevelItem(i);
                    return true;
                }
                // 递归查找子项
                if (findAndRemove(item)) {
                    return true;
                }
            }
        } else {
            // 处理子项
            for (int i = 0; i < parentItem->childCount(); ++i) {
                QTreeWidgetItem *childItem = parentItem->child(i);
                ContextTreeItem *contextChild = dynamic_cast<ContextTreeItem*>(childItem);
                if (contextChild && contextChild->contextItem().itemId == itemId) {
                    delete parentItem->takeChild(i);
                    return true;
                }
                // 递归查找更深层的子项
                if (findAndRemove(childItem)) {
                    return true;
                }
            }
        }
        return false;
    };

    if (findAndRemove(nullptr)) {
        emit contextItemsChanged();
    }
}

void ContextNavigationTree::clearContextItems()
{
    clear();
    // 清空过滤输入框
    if (mFilterLineEdit) {
        mFilterLineEdit->clear();
    }
    updateItemActions();
    emit contextItemsChanged();
}

QList<ContextItem> ContextNavigationTree::getSelectedContextItems() const
{
    QList<ContextItem> selectedContexts;

    int topItemCnt = topLevelItemCount();

    // 只处理顶层节点
    for (int i = 0; i < topItemCnt; ++i) {
        ContextTreeItem *topLevelItem = dynamic_cast<ContextTreeItem*>(this->topLevelItem(i));
        if (!topLevelItem || !topLevelItem->isChecked()) {
            continue;
        }

        ContextItem context = topLevelItem->contextItem();

        // 如果是文件夹类型，根据子节点选择状态构建内容
        if (context.type == ContextItem::Folder) {
            // 获取选中的子文件路径
            QStringList selectedChildPaths = getSelectedChildFilePaths(topLevelItem);

            // 如果有选中的子文件，重新构建内容
            if (!selectedChildPaths.isEmpty()) {
                context.buildSelectiveContent(selectedChildPaths);
            }
            // 如果没有选中任何子文件，但顶层节点被选中，使用原始内容
            // 原始内容已经在 context 中，无需额外处理
        }
        // 如果是文件类型，直接使用原始内容

        selectedContexts.append(context);
    }

    return selectedContexts;
}

QList<ContextItem> ContextNavigationTree::getAllContextItems() const
{
    QList<ContextItem> allContexts;

    for (int i = 0; i < topLevelItemCount(); ++i) {
        ContextTreeItem *item = dynamic_cast<ContextTreeItem*>(topLevelItem(i));
        if (item) {
            allContexts.append(item->contextItem());
        }
    }

    return allContexts;
}

QStringList ContextNavigationTree::getSelectedChildFilePaths(ContextTreeItem *topLevelItem) const
{
    QStringList selectedPaths;

    if (!topLevelItem) {
        return selectedPaths;
    }

    // 递归收集选中的子文件路径
    std::function<void(QTreeWidgetItem*)> collectSelectedFiles = [&](QTreeWidgetItem* item) {
        ContextTreeItem *contextItem = dynamic_cast<ContextTreeItem*>(item);
        if (contextItem && contextItem->isChecked()) {
            ContextItem context = contextItem->contextItem();
            // 只收集文件类型的路径
            if (context.type == ContextItem::File) {
                selectedPaths.append(context.uri);
            }
        }

        // 递归处理子项
        for (int i = 0; i < item->childCount(); ++i) {
            collectSelectedFiles(item->child(i));
        }
    };

    // 只收集子项，不包括顶层节点本身
    for (int i = 0; i < topLevelItem->childCount(); ++i) {
        collectSelectedFiles(topLevelItem->child(i));
    }

    return selectedPaths;
}

void ContextNavigationTree::setGitIgnoreRules(const QString &rules)
{
    if (mGitIgnoreParser) {
        mGitIgnoreParser->setIgnoreRules(rules);
    }
}

void ContextNavigationTree::setIncludeSubfolders(bool include)
{
    if (mIncludeSubfolders == include) {
        return; // 没有变化，直接返回
    }

    mIncludeSubfolders = include;

    // 重新构建所有文件夹的子项结构
    for (int i = 0; i < topLevelItemCount(); ++i) {
        ContextTreeItem *item = dynamic_cast<ContextTreeItem*>(topLevelItem(i));
        if (item && item->contextItem().type == ContextItem::Folder) {
            // 清除现有子项
            item->takeChildren();

            // 如果启用子文件夹，重新构建结构
            if (mIncludeSubfolders) {
                buildFolderStructure(item, item->contextItem().uri);
                item->setExpanded(true);
            }
        }
    }

    emit contextItemsChanged();
}

void ContextNavigationTree::selectAll()
{
    // 递归处理所有项目（包括子项）
    std::function<void(QTreeWidgetItem*)> selectAllRecursive = [&](QTreeWidgetItem* item) {
        ContextTreeItem *contextItem = dynamic_cast<ContextTreeItem*>(item);
        if (contextItem) {
            contextItem->setChecked(true);
        }

        for (int i = 0; i < item->childCount(); ++i) {
            selectAllRecursive(item->child(i));
        }
    };

    for (int i = 0; i < topLevelItemCount(); ++i) {
        selectAllRecursive(topLevelItem(i));
    }
    emit contextItemsChanged();
}

void ContextNavigationTree::selectNone()
{
    // 递归处理所有项目（包括子项）
    std::function<void(QTreeWidgetItem*)> selectNoneRecursive = [&](QTreeWidgetItem* item) {
        ContextTreeItem *contextItem = dynamic_cast<ContextTreeItem*>(item);
        if (contextItem) {
            contextItem->setChecked(false);
        }

        for (int i = 0; i < item->childCount(); ++i) {
            selectNoneRecursive(item->child(i));
        }
    };

    for (int i = 0; i < topLevelItemCount(); ++i) {
        selectNoneRecursive(topLevelItem(i));
    }
    emit contextItemsChanged();
}

void ContextNavigationTree::invertSelection()
{
    // 递归处理所有项目（包括子项），但只处理文件类型节点
    std::function<void(QTreeWidgetItem*)> invertRecursive = [&](QTreeWidgetItem* item) {
        ContextTreeItem *contextItem = dynamic_cast<ContextTreeItem*>(item);
        if (contextItem) {
            // 只处理文件类型节点，文件夹类型节点的选中状态由其子节点决定
            if (contextItem->contextItem().type == ContextItem::File) {
                contextItem->setChecked(!contextItem->isChecked());
            }
        }

        for (int i = 0; i < item->childCount(); ++i) {
            invertRecursive(item->child(i));
        }
    };

    for (int i = 0; i < topLevelItemCount(); ++i) {
        invertRecursive(topLevelItem(i));
    }
    emit contextItemsChanged();
}

void ContextNavigationTree::deselectSourceFiles()
{
    // 递归处理所有项目（包括子项），只处理源文件类型节点
    std::function<void(QTreeWidgetItem*)> deselectSourceRecursive = [&](QTreeWidgetItem* item) {
        ContextTreeItem *contextItem = dynamic_cast<ContextTreeItem*>(item);
        if (contextItem) {
            // 只处理文件类型节点
            if (contextItem->contextItem().type == ContextItem::File) {
                QString fileName = contextItem->contextItem().name;
                // 检查文件扩展名是否为.cpp或.c
                if (fileName.endsWith(".cpp", Qt::CaseInsensitive) || 
                    fileName.endsWith(".c", Qt::CaseInsensitive)) {
                    contextItem->setChecked(false);
                }
            }
        }

        for (int i = 0; i < item->childCount(); ++i) {
            deselectSourceRecursive(item->child(i));
        }
    };

    for (int i = 0; i < topLevelItemCount(); ++i) {
        deselectSourceRecursive(topLevelItem(i));
    }
    emit contextItemsChanged();
}

bool ContextNavigationTree::RefreshStats::isEmpty() const
{
    return addedFiles == 0 && addedFolders == 0 && removedFiles == 0 && removedFolders == 0;
}

QString ContextNavigationTree::RefreshStats::toString() const
{
    QStringList changes;
    if (addedFolders > 0)
        changes << QString("新增 %1 个文件夹").arg(addedFolders);
    if (addedFiles > 0)
        changes << QString("新增 %1 个文件").arg(addedFiles);
    if (removedFolders > 0)
        changes << QString("删除 %1 个文件夹").arg(removedFolders);
    if (removedFiles > 0)
        changes << QString("删除 %1 个文件").arg(removedFiles);
    return changes.join(", ");
}

void ContextNavigationTree::refreshContextItems()
{
    QMap<QString, QPair<Qt::CheckState, bool>> itemStates;
    std::function<void(QTreeWidgetItem *)> saveStates = [&](QTreeWidgetItem *item) {
        if (!item)
            return;
        ContextTreeItem *contextItem = dynamic_cast<ContextTreeItem *>(item);
        if (contextItem) {
            itemStates[contextItem->contextItem().uri] = {item->checkState(0), item->isExpanded()};
        }
        for (int i = 0; i < item->childCount(); ++i)
            saveStates(item->child(i));
    };

    std::function<void(QTreeWidgetItem *)> restoreStates = [&](QTreeWidgetItem *item) {
        if (!item)
            return;
        ContextTreeItem *contextItem = dynamic_cast<ContextTreeItem *>(item);
        if (contextItem) {
            QString uri = contextItem->contextItem().uri;
            if (itemStates.contains(uri)) {
                const auto &state = itemStates[uri];
                item->setCheckState(0, state.first);
                item->setExpanded(state.second);
            }
        }
        for (int i = 0; i < item->childCount(); ++i)
            restoreStates(item->child(i));
    };

    RefreshStats totalStats;

    for (int i = 0; i < topLevelItemCount(); ++i) {
        ContextTreeItem *item = dynamic_cast<ContextTreeItem *>(topLevelItem(i));
        if (item && item->contextItem().type == ContextItem::Folder) {
            saveStates(item);

            QSet<QString> initialUris;
            QMap<QString, ContextItem::ContextType> initialTypes;
            collectItemUrisAndTypes(item, initialUris, initialTypes);

            ContextItem context = item->contextItem();
            QFileInfo folderInfo(context.uri);
            if (folderInfo.exists() && folderInfo.isDir()) {
                QString content, treeStructure;
                int fileCount = 0;
                treeStructure += ".\n";
                treeStructure += "└── " + folderInfo.fileName() + "/\n";
                processDirectory(context.uri, content, fileCount, treeStructure, "    ");

                context.content = content;
                context.fileCount = fileCount;
                context.treeStructure = treeStructure;
                item->setContextItem(context);
                item->setToolTip(0, treeStructure);

                if (mIncludeSubfolders) {
                    item->takeChildren();
                    buildFolderStructure(item, context.uri);

                    QSet<QString> finalUris;
                    QMap<QString, ContextItem::ContextType> finalTypes;
                    collectItemUrisAndTypes(item, finalUris, finalTypes);

                    RefreshStats stats = calculateRefreshDiff(initialUris,
                                                              initialTypes,
                                                              finalUris,
                                                              finalTypes);
                    totalStats.addedFiles += stats.addedFiles;
                    totalStats.addedFolders += stats.addedFolders;
                    totalStats.removedFiles += stats.removedFiles;
                    totalStats.removedFolders += stats.removedFolders;

                    restoreStates(item);
                }
            }
        }
    }

    showRefreshStats(totalStats);
    emit contextItemsChanged();
}

void ContextNavigationTree::collectItemUrisAndTypes(
    QTreeWidgetItem *parent,
    QSet<QString> &uris,
    QMap<QString, ContextItem::ContextType> &types)
{
    std::function<void(QTreeWidgetItem *)> collectRecursive = [&](QTreeWidgetItem *item) {
        if (!item)
            return;
        ContextTreeItem *contextItem = dynamic_cast<ContextTreeItem *>(item);
        if (contextItem) {
            const auto &ctx = contextItem->contextItem();
            uris.insert(ctx.uri);
            types[ctx.uri] = ctx.type;
        }
        for (int i = 0; i < item->childCount(); ++i) {
            collectRecursive(item->child(i));
        }
    };

    for (int i = 0; i < parent->childCount(); ++i) {
        collectRecursive(parent->child(i));
    }
}

ContextNavigationTree::RefreshStats
ContextNavigationTree::calculateRefreshDiff(const QSet<QString> &initialUris,
                                           const QMap<QString, ContextItem::ContextType> &initialTypes,
                                           const QSet<QString> &finalUris,
                                           const QMap<QString, ContextItem::ContextType> &finalTypes)
{
    RefreshStats stats;
    QSet<QString> addedUris = finalUris - initialUris;
    QSet<QString> removedUris = initialUris - finalUris;

    for (const QString &uri : addedUris) {
        if (finalTypes.value(uri) == ContextItem::File)
            stats.addedFiles++;
        else
            stats.addedFolders++;
    }
    for (const QString &uri : removedUris) {
        if (initialTypes.value(uri) == ContextItem::File)
            stats.removedFiles++;
        else
            stats.removedFolders++;
    }
    return stats;
}

void ContextNavigationTree::showRefreshStats(const RefreshStats &stats)
{
    if (!stats.isEmpty()) {
        POPUPMSG->showMessage(this, stats.toString(), PopUpMessage::Correct, PopUpMessage::BottomLeft);
    }
    else
    {
        POPUPMSG->showMessage(this, "文件夹内容无变化", PopUpMessage::Info, PopUpMessage::BottomLeft);
    }
}

void ContextNavigationTree::updateItemActions()
{
    bool hasItems = topLevelItemCount() > 0;

    if (mDeleteAllAction) mDeleteAllAction->setEnabled(hasItems);
    if (mSelectAllAction) mSelectAllAction->setEnabled(hasItems);
    if (mSelectNoneAction) mSelectNoneAction->setEnabled(hasItems);
    if (mInvertSelectionAction) mInvertSelectionAction->setEnabled(hasItems);
    if (mRefreshAction) mRefreshAction->setEnabled(hasItems);
    if (mDeselectSourceFilesAction) mDeselectSourceFilesAction->setEnabled(hasItems);
}

void ContextNavigationTree::addDeleteButtonToItem(ContextTreeItem *item, const QString &itemId)
{
    // 创建删除按钮（使用QAction和QToolButton）
    QAction *deleteAction = new QAction(this);
    deleteAction->setIcon(DELETE_ICON.icon());
    deleteAction->setToolTip("删除");
    deleteAction->setProperty("contextItemId", itemId);
    connect(deleteAction, &QAction::triggered, this, &ContextNavigationTree::onDeleteItemClicked);

    QToolButton *deleteBtn = new QToolButton(this);
    deleteBtn->setDefaultAction(deleteAction);
    deleteBtn->setAutoRaise(true);  // 去除按钮轮廓
    deleteBtn->setMaximumSize(25, 25);
    setItemWidget(item, 1, deleteBtn);
}

void ContextNavigationTree::buildFolderStructure(ContextTreeItem *parentItem, const QString &folderPath)
{
    QDir dir(folderPath);
    QStringList entryList = dir.entryList(QDir::Files | QDir::Dirs | QDir::NoDotAndDotDot);

    // 分离文件和目录，并排序
    QStringList files, directories;
    for (const QString &entry : entryList) {
        QString fullPath = dir.filePath(entry);
        QFileInfo fileInfo(fullPath);

        if (fileInfo.isFile()) {
            // 检查文件是否应该被忽略
            if (mGitIgnoreParser && mGitIgnoreParser->shouldIgnoreFile(fullPath, folderPath)) {
                continue;
            }
            if (!fileIsTextFile(fullPath)) {
                continue;
            }
            files.append(entry);
        } else if (fileInfo.isDir()) {
            // 检查目录是否应该被忽略
            if (mGitIgnoreParser && mGitIgnoreParser->shouldIgnoreDirectory(fullPath, folderPath)) {
                continue;
            }
            directories.append(entry);
        }
    }

    // 排序
    files.sort();
    directories.sort();

    // 先添加目录
    for (const QString &dirName : directories) {
        QString fullPath = dir.filePath(dirName);

        // 创建虚拟的ContextItem用于显示
        ContextItem dirContext;
        dirContext.name = dirName;
        dirContext.type = ContextItem::Folder;
        dirContext.uri = fullPath;
        dirContext.description = fullPath;
        dirContext.itemId = QUuid::createUuid().toString();

        ContextTreeItem *dirItem = new ContextTreeItem(dirContext, parentItem);

        // 为子目录添加删除按钮
        addDeleteButtonToItem(dirItem, dirContext.itemId);

        // 递归添加子目录内容
        buildFolderStructure(dirItem, fullPath);
    }

    // 再添加文件
    for (const QString &fileName : files) {
        QString fullPath = dir.filePath(fileName);

        // 创建虚拟的ContextItem用于显示
        ContextItem fileContext;
        fileContext.name = fileName;
        fileContext.type = ContextItem::File;
        fileContext.uri = fullPath;
        fileContext.description = fullPath;
        fileContext.itemId = QUuid::createUuid().toString();

        ContextTreeItem *fileItem = new ContextTreeItem(fileContext, parentItem);

        // 为子文件添加删除按钮
        addDeleteButtonToItem(fileItem, fileContext.itemId);
    }
}

void ContextNavigationTree::dragEnterEvent(QDragEnterEvent *event)
{
    // 检查拖拽数据是否包含URL
    if (event->mimeData()->hasUrls()) {
        QList<QUrl> urlList = event->mimeData()->urls();
        bool hasValidFiles = false;

        for (const QUrl &url : urlList) {
            if (!url.isLocalFile())
                continue;

            QString path = url.toLocalFile();
            QFileInfo fileInfo(path);

            if (fileInfo.exists()) {
                // 如果是文件夹，接受拖拽
                if (fileInfo.isDir()) {
                    hasValidFiles = true;
                    continue;
                }

                // 如果是文件，检查是否为文本文件
                if (fileInfo.isFile()) {
                    if (fileIsTextFile(path)) {
                        hasValidFiles = true;
                    } else {
                        // 发现非文本文件，拒绝整个拖拽操作
                        event->ignore();
                        return;
                    }
                }
            }
        }

        if (hasValidFiles) {
            event->acceptProposedAction();
        } else {
            event->ignore();
        }
    } else {
        event->ignore();
    }
}

void ContextNavigationTree::dragMoveEvent(QDragMoveEvent *event)
{
    // 检查拖拽数据是否包含URL
    if (event->mimeData()->hasUrls()) {
        QList<QUrl> urlList = event->mimeData()->urls();
        bool hasValidFiles = false;

        for (const QUrl &url : urlList) {
            if (!url.isLocalFile())
                continue;

            QString path = url.toLocalFile();
            QFileInfo fileInfo(path);

            if (fileInfo.exists()) {
                // 如果是文件夹，接受拖拽
                if (fileInfo.isDir()) {
                    hasValidFiles = true;
                    continue;
                }

                // 如果是文件，检查是否为文本文件
                if (fileInfo.isFile()) {
                    if (fileIsTextFile(path)) {
                        hasValidFiles = true;
                    } else {
                        // 发现非文本文件，拒绝拖拽
                        event->ignore();
                        return;
                    }
                }
            }
        }

        if (hasValidFiles) {
            event->acceptProposedAction();
        } else {
            event->ignore();
        }
    } else {
        event->ignore();
    }
}

void ContextNavigationTree::dropEvent(QDropEvent *event)
{
    // 获取拖拽进来的数据
    const QMimeData *mimeData = event->mimeData();

    // 处理拖拽的URL数据
    if (mimeData->hasUrls()) {
        QList<QUrl> urlList = mimeData->urls();
        for (const QUrl &url : urlList) {
            if (!url.isLocalFile())
                continue;

            QString path = url.toLocalFile();
            QFileInfo fileInfo(path);

            if (fileInfo.exists()) {
                // 拖进来单个文件
                if (fileInfo.isFile()) {
                    // 检查文件是否为文本文件
                    if (!fileIsTextFile(path)) {
                        continue; // 跳过非文本文件
                    }

                    bool success = false;
                    ContextItem item = ContextItem::buildFileContextFromFilePath(Utils::FilePath::fromString(path), success);
                    if (success) {
                        addContextItem(item);
                    }
                }
                // 拖进来文件夹
                else if (fileInfo.isDir()) {
                    QString content;
                    int fileCount = 0;
                    QString treeStructure;

                    // 添加根目录到树形结构，使用点作为根标识
                    treeStructure += ".\n";
                    treeStructure += "└── " + fileInfo.fileName() + "/\n";

                    processDirectory(path, content, fileCount, treeStructure, "    ");

                    ContextItem item;
                    item.name = fileInfo.fileName();
                    item.description = fileInfo.path();
                    item.content = content;
                    item.type = ContextItem::Folder;
                    item.uri = path;
                    item.fileCount = fileCount;
                    item.treeStructure = treeStructure;

                    addContextItem(item);
                }
            }
        }
    }

    // 接受拖拽事件
    event->accept();
}

// 拖拽事件的公共接口函数实现
void ContextNavigationTree::handleDragEnterEvent(QDragEnterEvent *event)
{
    dragEnterEvent(event);
}

void ContextNavigationTree::handleDragMoveEvent(QDragMoveEvent *event)
{
    dragMoveEvent(event);
}

void ContextNavigationTree::handleDropEvent(QDropEvent *event)
{
    dropEvent(event);
}

void ContextNavigationTree::onItemChanged(QTreeWidgetItem *item, int column)
{
    if (column != 0) {
        return; // 只处理第一列的勾选状态变化
    }

    ContextTreeItem *contextItem = dynamic_cast<ContextTreeItem*>(item);
    if (!contextItem) {
        return;
    }

    // 临时断开信号连接，避免递归调用
    disconnect(this, &QTreeWidget::itemChanged, this, &ContextNavigationTree::onItemChanged);

    Qt::CheckState newState = contextItem->checkState(0);

    // 如果是父节点状态改变，更新所有子节点
    if (newState != Qt::PartiallyChecked) {
        updateChildrenCheckState(contextItem, newState);
    }

    // 更新父节点的状态
    updateParentCheckState(contextItem);

    // 重新连接信号
    connect(this, &QTreeWidget::itemChanged, this, &ContextNavigationTree::onItemChanged);

    emit contextItemsChanged();
}

void ContextNavigationTree::onCustomContextMenuRequested(const QPoint &pos)
{
    QTreeWidgetItem *item = itemAt(pos);
    if (!item) {
        return;
    }

    QMenu contextMenu(this);
    contextMenu.addAction(mDeleteAction);
    contextMenu.addAction(mToggleCheckAction);

    contextMenu.exec(mapToGlobal(pos));
}

void ContextNavigationTree::onDeleteItemClicked()
{
    QAction *action = qobject_cast<QAction*>(sender());
    if (!action) return;

    QString itemId = action->property("contextItemId").toString();
    removeContextItem(itemId);
}

// 递归处理文件夹的函数（复用CustomTextEdit的逻辑）
void ContextNavigationTree::processDirectory(const QString &path, QString &content, int &fileCount, QString &treeStructure, const QString &prefix, bool isLast)
{
    QDir dir(path);
    QStringList entryList = dir.entryList(QDir::Files | QDir::Dirs | QDir::NoDotAndDotDot);

    // 分离文件和目录，并排序
    QStringList files, directories;
    for (const QString &entry : entryList) {
        QString fullPath = dir.filePath(entry);
        QFileInfo fileInfo(fullPath);

        if (fileInfo.isFile()) {
            // 检查文件是否应该被忽略
            if (mGitIgnoreParser && mGitIgnoreParser->shouldIgnoreFile(fullPath, path)) {
                devLog(QString("[GitIgnore] 忽略文件: %1").arg(fullPath));
                continue;
            }
            if (!fileIsTextFile(fullPath)) {
                continue;
            }
            files.append(entry);
        } else if (fileInfo.isDir()) {
            // 检查目录是否应该被忽略
            if (mGitIgnoreParser && mGitIgnoreParser->shouldIgnoreDirectory(fullPath, path)) {
                devLog(QString("[GitIgnore] 忽略目录: %1").arg(fullPath));
                continue;
            }
            directories.append(entry);
        }
    }

    // 排序
    files.sort();
    directories.sort();

    // 合并列表，目录在前，文件在后
    QStringList allEntries = directories + files;

    for (int i = 0; i < allEntries.size(); ++i) {
        const QString &entry = allEntries.at(i);
        QString fullPath = dir.filePath(entry);
        QFileInfo fileInfo(fullPath);
        bool isLastEntry = (i == allEntries.size() - 1);

        // 构建树形结构字符串
        QString connector = isLastEntry ? "└── " : "├── ";
        treeStructure += prefix + connector + entry;

        if (fileInfo.isDir()) {
            treeStructure += "/\n";

            // 只有在启用子文件夹时才递归处理
            if (mIncludeSubfolders) {
                QString nextPrefix = prefix + (isLastEntry ? "    " : "│   ");
                processDirectory(fullPath, content, fileCount, treeStructure, nextPrefix, isLastEntry);
            }
        } else {
            treeStructure += "\n";

            // 读取文件内容
            bool success = false;
            QString fileContent = Internal::readTextFile(fullPath, success);
            if (success) {
                content += QString("=== %1 ===\n").arg(entry);
                content += fileContent;
                content += "\n\n";
                fileCount++;
            }
        }
    }
}


} // namespace CodeBooster::Internal
