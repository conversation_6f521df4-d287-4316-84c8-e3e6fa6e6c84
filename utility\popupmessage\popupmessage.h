#ifndef POPUPMESSAGE_H
#define POPUPMESSAGE_H

#include <QWidget>
#include <QList>
#include <QPointer>
#include <QLabel>
#include <QHBoxLayout>
#include <QPropertyAnimation>
#include <QTimer>
#include <QMutex>
#include "popup_resources.h"

class MessageWidget;

#define POPUPMSG PopUpMessage::getInstance()

class PopUpMessage : public QObject {
    Q_OBJECT
public:
    /** 消息类型 **/
    enum MsgType{
        Info,    ///< 提示消息
        Error,     ///< 错误消息
        Correct,   ///< 正确消息
        Warning   ///< 提醒消息
    };

    /** 显示方向 **/
    enum Direction {
        TopLeft,   ///< 左上角
        TopRight,  ///< 右上角
        TopCenter, ///< 顶部中间
        BottomLeft,  ///< 左下角
        BottomRight, ///< 右下角
        Center       ///< 居中
    };

public:
    static PopUpMessage *getInstance();
    void showMessage(QWidget *parentWidget, const QString &message, const MsgType &msgType = MsgType::Correct, const Direction &direction = Direction::Center);

private:
    PopUpMessage();
    ~PopUpMessage();

    void adjustPositions(QWidget *parentWidget, Direction direction);

private:
    QMap<QWidget*, QMap<Direction, QList<QPointer<MessageWidget>>>> messagesMap;
    QMutex mMsgMutex;
    static PopUpMessage *mInstance;
};

class MessageWidget : public QFrame {
    Q_OBJECT
public:
    static int MessageInterval();

    explicit MessageWidget(QWidget *parent = nullptr);
    void setMessage(const QString &message, const QPixmap &icon, PopUpMessage::MsgType msgType);
    void showAnimated(int duration, int offset = 0, PopUpMessage::Direction direction = PopUpMessage::Center);
    void hideAnimated(PopUpMessage::Direction direction);
    bool animationProcessing() const;

private:
    QLabel *iconLabel;
    QLabel *messageLabel;
    bool inAnimation;
    PopUpMessage::MsgType m_msgType;
};

#endif // POPUPMESSAGE_H
