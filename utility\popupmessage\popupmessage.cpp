#include "popupmessage.h"
#include <QDebug>

PopUpMessage *PopUpMessage::mInstance = nullptr;

PopUpMessage::PopUpMessage() {}

PopUpMessage::~PopUpMessage() {}

PopUpMessage *PopUpMessage::getInstance()
{
    if(mInstance == nullptr) {
        mInstance = new PopUpMessage();
    }
    return mInstance;
}

void PopUpMessage::showMessage(QWidget *parentWidget, const QString &message, const MsgType &msgType, const Direction &direction) {
    QMutexLocker locker(&mMsgMutex);

    QPixmap icon;
    switch (msgType) {
    case Error:
        icon.loadFromData(popup_error_data, popup_error_data_size, "PNG");
        break;
    case Correct:
        icon.loadFromData(popup_correct_data, popup_correct_data_size, "PNG");
        break;
    case Warning:
        icon.loadFromData(popup_warning_data, popup_warning_data_size, "PNG");
        break;
    case Info:
        icon.loadFromData(popup_info_data, popup_info_data_size, "PNG");
        break;
    }

    // 调整图标大小为24
    icon = icon.scaled(24, 24, Qt::KeepAspectRatio, Qt::SmoothTransformation);

    // 创建新的消息控件
    MessageWidget *newMessage = new MessageWidget(parentWidget);
    newMessage->setMessage(message, icon, msgType);

    // 获取或创建特定方向的消息列表
    QList<QPointer<MessageWidget>> &messages = messagesMap[parentWidget][direction];

    // 计算新消息的位置
    int yOffset = 0;
    if (direction == TopLeft || direction == TopRight || direction == TopCenter) {
        // 从顶部弹出，新增消息显示在现有消息下方
        for (MessageWidget *msg : messages) {
            yOffset += msg->height() + MessageWidget::MessageInterval();
        }
    } else if (direction == BottomLeft || direction == BottomRight) {
        // 从底部弹出，新增消息显示在现有消息上方
        yOffset = parentWidget->height() - newMessage->height();
        for (MessageWidget *msg : messages) {
            yOffset -= msg->height() + MessageWidget::MessageInterval();
        }
    } else {
        // 居中显示
        // 1. 计算所有消息（包括新消息）的总高度
        int totalHeight = newMessage->height();
        for (MessageWidget *msg : messages) {
            totalHeight += msg->height() + MessageWidget::MessageInterval();
        }

        // 2. 计算起始Y坐标，使整个消息块垂直居中
        int yPos = (parentWidget->height() - totalHeight) / 2;

        // 3. 调整现有消息的位置（带动画）
        for (MessageWidget *msg : messages) {
            QPropertyAnimation *anim = new QPropertyAnimation(msg, "pos");
            anim->setDuration(150); // 使用较短的动画时间进行调整
            anim->setEndValue(QPoint(msg->x(), yPos));
            anim->start(QAbstractAnimation::DeleteWhenStopped);
            yPos += msg->height() + MessageWidget::MessageInterval();
        }

        // 4. 使用计算出的yOffset来显示新消息
        yOffset = yPos;
    }

    newMessage->showAnimated(300, yOffset, direction);

    // 将新消息添加到列表中
    messages.append(newMessage);

    // 连接信号，当消息消失时从列表中移除
    connect(newMessage, &MessageWidget::destroyed, this, [this, parentWidget, newMessage, direction]() {
        QMap<Direction, QList<QPointer<MessageWidget>>> &directionMap = messagesMap[parentWidget];
        QList<QPointer<MessageWidget>> &messages = directionMap[direction];
        messages.removeOne(newMessage);

        // 如果特定方向的消息列表为空，则从方向映射中移除该方向
        if (messages.isEmpty()) {
            directionMap.remove(direction);
        }

        // 如果所有方向的消息列表都为空，则从主映射中移除 parentWidget
        if (directionMap.isEmpty()) {
            messagesMap.remove(parentWidget);
        } else {
            adjustPositions(parentWidget, direction);
        }
    });
}


void PopUpMessage::adjustPositions(QWidget *parentWidget, Direction direction) {
    if (!messagesMap.contains(parentWidget) || !messagesMap[parentWidget].contains(direction)) {
        return;
    }
    QList<QPointer<MessageWidget>> &messages = messagesMap[parentWidget][direction];

    if (messages.isEmpty()) {
        return; // 如果消息列表为空，直接返回
    }

    int yOffset = 0;

    if (direction == TopLeft || direction == TopRight || direction == TopCenter) {
        // 从顶部弹出，消息从下到上排列
        for (MessageWidget *msg : messages) {
            if (msg->animationProcessing())
                continue;

            int xPos;
            if (direction == TopLeft) {
                xPos = 0;
            } else if (direction == TopRight) {
                xPos = parentWidget->width() - msg->width();
            } else { // TopCenter
                xPos = parentWidget->width() / 2 - msg->width() / 2;
            }

            QPropertyAnimation *anim = new QPropertyAnimation(msg, "pos");
            anim->setDuration(150);
            anim->setEndValue(QPoint(xPos, yOffset));
            anim->start(QAbstractAnimation::DeleteWhenStopped);
            yOffset += msg->height() + MessageWidget::MessageInterval();
        }
    } else if (direction == BottomLeft || direction == BottomRight) {
        // 从底部弹出，消息从下到上排列
        yOffset = parentWidget->height();
        for (MessageWidget *msg : messages) {
            if (msg->animationProcessing())
                continue;
            yOffset -= msg->height() + MessageWidget::MessageInterval();
            msg->move(parentWidget->width() / 2 - msg->width() / 2, yOffset);
        }
    } else {
        // 居中显示
        int totalHeight = 0;
        for (MessageWidget *msg : messages) {
            totalHeight += msg->height() + MessageWidget::MessageInterval();
        }
        // 减去最后一个间隔
        if (!messages.isEmpty()) {
            totalHeight -= MessageWidget::MessageInterval();
        }

        yOffset = (parentWidget->height() - totalHeight) / 2;

        for (MessageWidget *msg : messages) {
            if (msg->animationProcessing())
                continue;
            msg->move(parentWidget->width() / 2 - msg->width() / 2, yOffset);
            yOffset += msg->height() + MessageWidget::MessageInterval();
        }
    }
}
int MessageWidget::MessageInterval() {
    return 6;
}

MessageWidget::MessageWidget(QWidget *parent) : QFrame(parent), inAnimation(false), m_msgType(PopUpMessage::Correct) {
    setObjectName("MessageWidget");

    // 设置控件的布局
    QHBoxLayout *layout = new QHBoxLayout(this);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->setSpacing(0);

    iconLabel = new QLabel(this);
    iconLabel->setFixedSize(32, 32);
    iconLabel->setAlignment(Qt::AlignCenter);

    messageLabel = new QLabel(this);
    messageLabel->setAlignment(Qt::AlignVCenter);
    messageLabel->setStyleSheet("color: black; padding-left: 10px;");

    layout->addWidget(iconLabel);
    layout->addWidget(messageLabel, 1);

    setLayout(layout);

    // 设置样式
    setStyleSheet("QFrame#MessageWidget{border: 1px solid black;}");

    // 设置最小大小
    setMinimumWidth(200);
    setFixedHeight(32);

    // 设置大小策略
    setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
}

void MessageWidget::setMessage(const QString &message, const QPixmap &icon, PopUpMessage::MsgType msgType) {
    m_msgType = msgType;
    messageLabel->setText(message);
    iconLabel->setPixmap(icon);

    QString iconBgColor;
    QString msgBgColor;

    switch (m_msgType) {
    case PopUpMessage::Correct:
        iconBgColor = "#2E7D32"; // 深绿
        msgBgColor = "#C8E6C9"; // 浅绿
        break;
    case PopUpMessage::Info:
        iconBgColor = "#1976D2"; // 深蓝
        msgBgColor = "#BBDEFB"; // 浅蓝
        break;
    case PopUpMessage::Warning:
        iconBgColor = "#F57F17"; // 深黄
        msgBgColor = "#FFF9C4"; // 浅黄
        break;
    case PopUpMessage::Error:
        iconBgColor = "#C62828"; // 深红
        msgBgColor = "#FFCDD2"; // 浅红
        break;
    }

    iconLabel->setStyleSheet(QString("background-color: %1;").arg(iconBgColor));
    messageLabel->setStyleSheet(QString("background-color: %1; color: black; padding-left: 10px;").arg(msgBgColor));
    setStyleSheet(QString("QFrame#MessageWidget { border: 1px solid %1; }").arg(iconBgColor));

    // 动态调整大小
    adjustSize();
}

void MessageWidget::showAnimated(int duration, int offset, PopUpMessage::Direction direction) {
    inAnimation = true;

    // 根据方向设置初始位置
    QPoint startPos;
    QPoint endPos;

    switch (direction) {
    case PopUpMessage::TopLeft:
        startPos = QPoint(-width(), offset);
        endPos = QPoint(0, offset);
        break;
    case PopUpMessage::TopRight:
        startPos = QPoint(parentWidget()->width(), offset);
        endPos = QPoint(parentWidget()->width() - width(), offset);
        break;
    case PopUpMessage::TopCenter:
        startPos = QPoint(parentWidget()->width() / 2 - width() / 2, -height());
        endPos = QPoint(parentWidget()->width() / 2 - width() / 2, offset);
        break;
    case PopUpMessage::BottomLeft:
        startPos = QPoint(-width(), offset);
        endPos = QPoint(0, offset);
        break;
    case PopUpMessage::BottomRight:
        startPos = QPoint(parentWidget()->width(), offset);
        endPos = QPoint(parentWidget()->width() - width(), offset);
        break;
    case PopUpMessage::Center:
        startPos = QPoint(parentWidget()->width() / 2 - width() / 2, offset);
        endPos   = QPoint(parentWidget()->width() / 2 - width() / 2, offset);
        break;
    }

    move(startPos);
    show();

    // 创建动画
    QPropertyAnimation *animation = new QPropertyAnimation(this, "pos");
    animation->setDuration(duration);
    animation->setStartValue(startPos);
    animation->setEndValue(endPos);
    animation->start(QAbstractAnimation::DeleteWhenStopped);

    // 使用 lambda 表达式调用 hideAnimated，并传递 direction 参数
    QTimer::singleShot(1200, this, [this, direction]() {
        hideAnimated(direction);
    });
}

void MessageWidget::hideAnimated(PopUpMessage::Direction direction) {
    inAnimation = true;

    QPoint endPos;
    switch (direction) {
    case PopUpMessage::TopLeft:
    case PopUpMessage::BottomLeft:
        endPos = QPoint(-width(), pos().y());
        break;
    case PopUpMessage::TopRight:
    case PopUpMessage::BottomRight:
        endPos = QPoint(parentWidget()->width(), pos().y());
        break;
    case PopUpMessage::TopCenter:
        // 从顶部消失
        endPos = QPoint(pos().x(), -height());
        break;
    case PopUpMessage::Center: {
        // 居中消失（淡出）
        QPropertyAnimation *fadeAnim = new QPropertyAnimation(this, "windowOpacity");
        fadeAnim->setDuration(300);
        fadeAnim->setStartValue(1.0);
        fadeAnim->setEndValue(0.0);
        connect(fadeAnim, &QPropertyAnimation::finished, this, &MessageWidget::deleteLater);
        fadeAnim->start(QAbstractAnimation::DeleteWhenStopped);
        return; // 直接返回，因为我们使用的是不同的动画
    }
    }

    QPropertyAnimation *animation = new QPropertyAnimation(this, "pos");
    animation->setDuration(500);
    animation->setStartValue(pos());
    animation->setEndValue(endPos);

    // 确保动画完成后调用 deleteLater
    connect(animation, &QPropertyAnimation::finished, this, &MessageWidget::deleteLater);

    animation->start(QAbstractAnimation::DeleteWhenStopped);
}

bool MessageWidget::animationProcessing() const {
    return inAnimation;
}
