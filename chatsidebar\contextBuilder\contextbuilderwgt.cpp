#include "contextbuilderwgt.h"

#include <QApplication>
#include <QClipboard>
#include <QHeaderView>
#include <QMenu>
#include <QMessageBox>
#include <QSplitter>
#include <QScrollArea>
#include <QTimer>
#include <functional>
#include <QScrollBar>

#include "common/codeboosterutils.h"
#include "common/codeboostericons.h"
#include "common/widgettheme.h"
#include "common/useroptions.h"
#include "pluginsettings/codeboostersettings.h"
#include "utility/gitignoreparser.h"
#include "ContextTree.h"
#include "instructioneditdialog.h"

namespace CodeBooster::Internal {

const int ContextBuilderWgt::UPDATE_PROMPT_TIMEOUT_MS = 500;

// -------------------------------------------------------------------------
// DragOverlayWidget 实现
// -------------------------------------------------------------------------

DragOverlayWidget::DragOverlayWidget(QWidget *parent)
    : QWidget(parent)
{
    setAttribute(Qt::WA_TransparentForMouseEvents);
    setStyleSheet(
        "DragOverlayWidget {"
        "    background-color: rgba(128, 128, 128, 0.1);"
        "    border: 2px dashed rgba(128, 128, 128, 0.8);"
        "    border-radius: 8px;"
        "}"
        );
}

void DragOverlayWidget::paintEvent(QPaintEvent *event)
{
    QWidget::paintEvent(event);

    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    // 获取图标
    QIcon icon = CodeBooster::ICON_ADDCONTEXT.icon();
    QPixmap pixmap = icon.pixmap(icon.actualSize(QSize(256, 256)));

    // 计算居中位置
    QRect iconRect;
    iconRect.setSize(pixmap.size());
    iconRect.moveCenter(rect().center());
    iconRect.moveTop(rect().center().y() - pixmap.height() / 2 - 20);

    // 绘制图标
    painter.drawPixmap(iconRect, pixmap);

    // 绘制文字
    painter.setPen(QColor(128, 128, 128));
    QFont font = painter.font();
    font.setPointSize(14);
    font.setBold(true);
    painter.setFont(font);

    QRect textRect = rect();
    textRect.setTop(iconRect.bottom() + 10);
    painter.drawText(textRect, Qt::AlignHCenter | Qt::AlignTop, "将文件拖放到此处以添加到您的消息中");
}


// -------------------------------------------------------------------------
// ContextBuilderWgt 实现
// -------------------------------------------------------------------------

ContextBuilderWgt::ContextBuilderWgt(QWidget *parent)
    : QWidget(parent)
    , mMainLayout(nullptr)
    , mTabWidget(nullptr)
    , mContextTab(nullptr)
    , mOptionsTab(nullptr)
    , mFilterGroupBox(nullptr)
    , mIncludeSubfoldersYes(nullptr)
    , mIncludeSubfoldersNo(nullptr)
    , mFilterRulesEdit(nullptr)
    , mResetFilterRulesBtn(nullptr)
    , mApplyFilterRulesBtn(nullptr)
    , mFormatGroupBox(nullptr)
    , mSourceCodeOnTopCheckBox(nullptr)
    , mContextGroupBox(nullptr)
    , mContextTree(nullptr)
    , mInstructionTabWidget(nullptr)
    , mInstructionInputTab(nullptr)
    , mInstructionManagementTab(nullptr)
    , mInstructionEdit(nullptr)
    , mInstructionTable(nullptr)
    , mNewInstructionBtn(nullptr)
    , mEditInstructionBtn(nullptr)
    , mDeleteInstructionBtn(nullptr)
    , mUseInstructionBtn(nullptr)
    , mPromptGroupBox(nullptr)
    , mFinalPromptEdit(nullptr)
    , mSendToChatBtn(nullptr)
    , mUpdatePromptTimer(nullptr)
    , mGitIgnoreParser(nullptr)
    , mDragOverlay(nullptr)
    , mSpinner(nullptr)
{
    setAcceptDrops(true);
    setupUI();
    createDragOverlay();

    // 初始化 GitIgnore 解析器
    mGitIgnoreParser = new GitIgnoreParser();

    // 初始化定时器
    mUpdatePromptTimer = new QTimer(this);
    mUpdatePromptTimer->setSingleShot(true);
    mUpdatePromptTimer->setInterval(1000);

    // 初始化Spinner
    mSpinner = new SpinnerSolution::Spinner(SpinnerSolution::SpinnerSize::Medium, mFinalPromptEdit);
    mSpinner->setVisible(false);

    // 初始化默认过滤规则（在UI创建之后）
    if (mFilterRulesEdit) {
        // 从UserOptions加载过滤规则
        QString savedRules = USER_OP.contextBuilderFilterRules;
        if (!savedRules.isEmpty()) {
            mFilterRulesEdit->setPlainText(savedRules);
        } else {
            QString defaultRules = CodeBoosterSettings::instance().getDefaultGitIgnoreRules();
            mFilterRulesEdit->setPlainText(defaultRules);
        }
        onFilterRulesChanged();
    }

    // 从UserOptions加载其他设置
    if (mIncludeSubfoldersYes && mIncludeSubfoldersNo) {
        bool includeSubfolders = USER_OP.contextBuilderIncludeSubfolders;
        mIncludeSubfoldersYes->setChecked(includeSubfolders);
        mIncludeSubfoldersNo->setChecked(!includeSubfolders);
    }
    if (mSourceCodeOnTopCheckBox) {
        mSourceCodeOnTopCheckBox->setChecked(USER_OP.contextBuilderSourceCodeOnTop);
    }

    setupConnections();
    loadInstructions();
}

ContextBuilderWgt::~ContextBuilderWgt()
{
    delete mGitIgnoreParser;
}

void ContextBuilderWgt::setupUI()
{
    mMainLayout = new QVBoxLayout(this);
    mMainLayout->setContentsMargins(2, 0, 2, 0);
    mMainLayout->setSpacing(8);

    // 创建Tab控件
    mTabWidget = new QTabWidget(this);

    // 创建上下文Tab
    setupContextTab();

    // 创建选项Tab
    setupOptionsTab();

    // 添加Tab到主布局
    mMainLayout->addWidget(mTabWidget);
}

void ContextBuilderWgt::setupContextTab()
{
    mContextTab = new QWidget();
    QVBoxLayout *contextTabLayout = new QVBoxLayout(mContextTab);
    contextTabLayout->setContentsMargins(8, 8, 8, 8);
    contextTabLayout->setSpacing(8);

    // 1. 上下文导航树区域
    mContextGroupBox = new QGroupBox("上下文 - 拖拽文件或文件夹到此处添加上下文", this);
    {
        QVBoxLayout *contextLayout = new QVBoxLayout(mContextGroupBox);

        mContextTree = new ContextNavigationTree(this);
        mContextTree->setAcceptDrops(false); // 将事件交给外部控件处理
        mContextTree->setMinimumHeight(300);
        // 设置默认的2px灰色虚线边框
        mContextTree->setStyleSheet("QTreeWidget { border: 2px dashed gray; }");

        // 发送到对话框按钮
        mSendToChatBtn = new QPushButton("发送到对话框", this);
        mSendToChatBtn->setEnabled(false);

        // 为发送按钮创建水平布局
        QHBoxLayout *sendButtonLayout = new QHBoxLayout();
        sendButtonLayout->addStretch(); // 添加弹簧，使按钮右对齐
        sendButtonLayout->addWidget(mSendToChatBtn);

        // 添加工具栏到上下文树的上方
        contextLayout->addWidget(mContextTree->getToolBar());

        // 初始化并添加过滤输入框到工具栏下面一行
        mContextTree->setUpFilterLineEdit();
        contextLayout->addWidget(mContextTree->getFilterLineEdit());

        contextLayout->addWidget(mContextTree);
        contextLayout->addLayout(sendButtonLayout);
    }

    // 2. 指令输入区域
    setupInstructionTabs();

    // 3. 最终Prompt框
    mPromptGroupBox = new QGroupBox("最终 Prompt", this);
    {
        QVBoxLayout *promptLayout = new QVBoxLayout(mPromptGroupBox);

        mFinalPromptEdit = new QPlainTextEdit(this);
        mFinalPromptEdit->setReadOnly(true);
        mFinalPromptEdit->setMinimumHeight(100);
        mFinalPromptEdit->setPlaceholderText("最终的 Prompt 内容将在这里显示...");

        promptLayout->addWidget(mFinalPromptEdit);

        // 添加复制按钮
        QHBoxLayout *promptButtonLayout = new QHBoxLayout();
        promptButtonLayout->addStretch(); // 添加弹簧，使按钮右对齐
        mCopyPromptBtn = new QPushButton("复制", this);
        mCopyPromptBtn->setToolTip("复制最终Prompt内容到剪切板");
        promptButtonLayout->addWidget(mCopyPromptBtn);
        promptLayout->addLayout(promptButtonLayout);
    }

    // 添加到上下文Tab布局
    contextTabLayout->addWidget(mContextGroupBox, 1); // 给上下文区域更多空间
    contextTabLayout->addWidget(mInstructionTabWidget);
    contextTabLayout->addWidget(mPromptGroupBox, 1); // 给Prompt区域更多空间

    // 添加到TabWidget
    mTabWidget->addTab(mContextTab, "上下文");
}

void ContextBuilderWgt::setupOptionsTab()
{
    mOptionsTab = new QWidget();
    QVBoxLayout *optionsTabLayout = new QVBoxLayout(mOptionsTab);
    optionsTabLayout->setContentsMargins(8, 8, 8, 8);
    optionsTabLayout->setSpacing(2);

    // 文件夹内容过滤选项区域
    mFilterGroupBox = new QGroupBox("文件夹内容过滤选项", this);
    {
        QVBoxLayout *filterLayout = new QVBoxLayout(mFilterGroupBox);

        // 读取子文件夹选项
        QHBoxLayout *subfolderLayout = new QHBoxLayout();
        QLabel *subfolderLabel = new QLabel("读取子文件夹：", this);
        mIncludeSubfoldersYes = new QRadioButton("是", this);
        mIncludeSubfoldersNo = new QRadioButton("否", this);
        mIncludeSubfoldersYes->setChecked(true); // 默认选中

        subfolderLayout->addWidget(subfolderLabel);
        subfolderLayout->addWidget(mIncludeSubfoldersYes);
        subfolderLayout->addWidget(mIncludeSubfoldersNo);
        subfolderLayout->addStretch();

        // 过滤规则输入区域
        QLabel *rulesLabel = new QLabel("忽略规则（.gitignore 格式）：", this);

        mFilterRulesEdit = new QTextEdit(this);
        mFilterRulesEdit->setMaximumHeight(200);
        mFilterRulesEdit->setPlaceholderText("输入 .gitignore 格式的过滤规则，留空使用默认规则...");

        // 按钮区域
        QHBoxLayout *buttonLayout = new QHBoxLayout();
        mResetFilterRulesBtn = new QPushButton("重置为默认", this);
        mApplyFilterRulesBtn = new QPushButton("应用规则", this);

        buttonLayout->addWidget(mResetFilterRulesBtn);
        buttonLayout->addWidget(mApplyFilterRulesBtn);
        buttonLayout->addStretch(1);


        // 提示信息
        QLabel *infoLabel = new QLabel("媒体文件、二进制文件和大于 500KB 的文本文件会被自动忽略", this);
        infoLabel->setStyleSheet("color: #666; font-size: 11px;");

        filterLayout->addLayout(subfolderLayout);
        filterLayout->addWidget(rulesLabel);
        filterLayout->addWidget(mFilterRulesEdit);
        filterLayout->addLayout(buttonLayout);
        filterLayout->addWidget(infoLabel);
        filterLayout->addStretch();
    }

    // 格式设置区域
    mFormatGroupBox = new QGroupBox("格式", this);
    {
        QVBoxLayout *formatLayout = new QVBoxLayout(mFormatGroupBox);

        mSourceCodeOnTopCheckBox = new QCheckBox("源代码在顶部", this);
        mSourceCodeOnTopCheckBox->setToolTip("选中时，用户指令将放在代码上下文底部；否则放在顶部");
        mSourceCodeOnTopCheckBox->setChecked(false); // 默认不选中，指令在顶部

        formatLayout->addWidget(mSourceCodeOnTopCheckBox);
        formatLayout->addStretch();
    }

    // 添加到选项Tab布局
    optionsTabLayout->addWidget(mFilterGroupBox);
    optionsTabLayout->addWidget(mFormatGroupBox);
    optionsTabLayout->addStretch(1); // 添加弹性空间

    // 添加到TabWidget
    mTabWidget->addTab(mOptionsTab, "选项");
}



void ContextBuilderWgt::setupConnections()
{
    // 过滤规则相关
    connect(mIncludeSubfoldersYes, &QRadioButton::toggled, this, &ContextBuilderWgt::onIncludeSubfoldersChanged);
    connect(mIncludeSubfoldersNo, &QRadioButton::toggled, this, &ContextBuilderWgt::onIncludeSubfoldersChanged);

    connect(mResetFilterRulesBtn, &QPushButton::clicked, this, &ContextBuilderWgt::onResetFilterRules);
    connect(mApplyFilterRulesBtn, &QPushButton::clicked, this, &ContextBuilderWgt::onFilterRulesChanged);

    connect(mFilterRulesEdit, &QTextEdit::textChanged, this, &ContextBuilderWgt::onFilterRulesChanged);

    // 格式设置相关
    connect(mSourceCodeOnTopCheckBox, &QCheckBox::toggled, this, &ContextBuilderWgt::onSourceCodeOnTopChanged);

    // 指令输入相关
    connect(mInstructionEdit, &QTextEdit::textChanged, this, &ContextBuilderWgt::onInstructionTextChanged);

    // 上下文树相关
    connect(mContextTree, &ContextNavigationTree::contextItemsChanged, this, &ContextBuilderWgt::onContextItemsChanged);

    // 定时器相关
    connect(mUpdatePromptTimer, &QTimer::timeout, this, &ContextBuilderWgt::onUpdatePromptTimer);

    // 按钮相关
    connect(mSendToChatBtn, &QPushButton::clicked, this, &ContextBuilderWgt::onSendToChat);
    connect(mCopyPromptBtn, &QPushButton::clicked, this, &ContextBuilderWgt::onCopyPrompt);
    connect(mNewInstructionBtn, &QPushButton::clicked, this, &ContextBuilderWgt::onNewInstruction);
}

void ContextBuilderWgt::setupInstructionTabs()
{
    mInstructionTabWidget = new QTabWidget(this);

    // 指令输入Tab
    mInstructionInputTab = new QWidget();
    QVBoxLayout *inputLayout = new QVBoxLayout(mInstructionInputTab);
    mInstructionEdit = new QTextEdit(this);
    mInstructionEdit->setAcceptDrops(false);
    mInstructionEdit->setPlaceholderText("输入您的提示词指令...");
    inputLayout->addWidget(mInstructionEdit);
    mInstructionTabWidget->addTab(mInstructionInputTab, "指令输入");

    // 指令管理Tab
    mInstructionManagementTab = new QWidget();
    QVBoxLayout *managementLayout = new QVBoxLayout(mInstructionManagementTab);

    // 创建指令管理表格
    mInstructionTable = new QTableWidget(this);
    mInstructionTable->setColumnCount(2);
    mInstructionTable->setHorizontalHeaderLabels(QStringList() << "标题" << "内容");
    mInstructionTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    mInstructionTable->setEditTriggers(QAbstractItemView::NoEditTriggers);
    mInstructionTable->horizontalHeader()->setStretchLastSection(true);

    // 创建按钮
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    mNewInstructionBtn = new QPushButton("新建", this);
    mEditInstructionBtn = new QPushButton("编辑", this);
    mDeleteInstructionBtn = new QPushButton("删除", this);
    mUseInstructionBtn = new QPushButton("使用", this);
    buttonLayout->addWidget(mNewInstructionBtn);
    buttonLayout->addWidget(mEditInstructionBtn);
    buttonLayout->addWidget(mDeleteInstructionBtn);
    buttonLayout->addStretch();
    buttonLayout->addWidget(mUseInstructionBtn);

    managementLayout->addWidget(mInstructionTable);
    managementLayout->addLayout(buttonLayout);

    mInstructionTabWidget->addTab(mInstructionManagementTab, "指令管理");
}

void ContextBuilderWgt::addContextItem(const ContextItem &context)
{
    mContextTree->addContextItem(context);
}

void ContextBuilderWgt::clearContextItems()
{
    mContextTree->clearContextItems();
    mInstructionEdit->clear();
    mFinalPromptEdit->clear();
    mContextGroupBox->setTitle("上下文 - 拖拽文件或文件夹到此处添加上下文");
}

void ContextBuilderWgt::onInstructionTextChanged()
{
    // 显示Spinner
    if (mSpinner) {
        mSpinner->show();
    }
    // 重置定时器，延迟更新Prompt
    mUpdatePromptTimer->start(UPDATE_PROMPT_TIMEOUT_MS);
}

void ContextBuilderWgt::onUpdatePromptTimer()
{
    updateFinalPrompt();
    // 隐藏Spinner
    if (mSpinner) {
        mSpinner->hide();
    }
}

void ContextBuilderWgt::onContextItemsChanged()
{
    updateFinalPrompt();

    // 更新上下文组框标题
    QList<ContextItem> allContexts = mContextTree->getAllContextItems();
    if (allContexts.isEmpty()) {
        mContextGroupBox->setTitle("上下文 - 拖拽文件或文件夹到此处添加上下文");
    } else {
        int totalFiles = 0;
        int totalFolders = 0;
        for (const ContextItem &context : allContexts) {
            if (context.type == ContextItem::File) {
                totalFiles++;
            } else if (context.type == ContextItem::Folder) {
                totalFolders++;
                totalFiles += context.fileCount; // 文件夹中的文件数
            }
        }
        mContextGroupBox->setTitle(QString("上下文 - 共 %1 个上下文项（%2 个文件夹，%3 个文件）")
                                       .arg(allContexts.size()).arg(totalFolders).arg(totalFiles));
    }
}

void ContextBuilderWgt::onSendToChat()
{
    QList<ContextItem> selectedContexts = mContextTree->getSelectedContextItems();
    if (!selectedContexts.isEmpty()) {
        emit sendContextToChat(selectedContexts);
    }
}

void ContextBuilderWgt::onNewInstruction()
{
    InstructionEditDialog dialog(this);
    if (dialog.exec() == QDialog::Accepted) {
        ContextInstruction instruction = dialog.getInstruction();
        mInstructions.append(instruction);
        loadInstructions(); // Refresh the table
    }
}

void ContextBuilderWgt::loadInstructions()
{
    // TODO: Load from a persistent storage
    if (mInstructions.isEmpty()) {
        mInstructions.append({ "翻译", "将以下代码翻译成英文" });
        mInstructions.append({ "代码审查", "审查以下代码，并提出改进建议" });
    }

    mInstructionTable->setRowCount(mInstructions.size());
    for (int i = 0; i < mInstructions.size(); ++i) {
        QTableWidgetItem *titleItem = new QTableWidgetItem(mInstructions[i].title);
        QTableWidgetItem *contentItem = new QTableWidgetItem(mInstructions[i].content);
        mInstructionTable->setItem(i, 0, titleItem);
        mInstructionTable->setItem(i, 1, contentItem);
    }
}

void ContextBuilderWgt::onCopyPrompt()
{
    QString promptText = mFinalPromptEdit->toPlainText();
    if (!promptText.isEmpty()) {
        QClipboard *clipboard = QApplication::clipboard();
        clipboard->setText(promptText);

        // 可以添加一个简单的提示，表示复制成功
        mCopyPromptBtn->setText("已复制");
        mCopyPromptBtn->setStyleSheet("color: green;");
        QTimer::singleShot(1000, [this]() {
            mCopyPromptBtn->setText("复制");
            mCopyPromptBtn->setStyleSheet("");
        });
    }
}

void ContextBuilderWgt::onFilterRulesChanged()
{
    // 检查UI是否已初始化
    if (!mFilterRulesEdit) {
        return;
    }

    // 获取过滤规则
    QString rules = mFilterRulesEdit->toPlainText().trimmed();

    // 如果没有自定义规则，使用默认规则
    if (rules.isEmpty()) {
        rules = CodeBoosterSettings::instance().getDefaultGitIgnoreRules();
    }

    // 更新GitIgnore解析器
    if (mGitIgnoreParser) {
        mGitIgnoreParser->setIgnoreRules(rules);
    }

    // 更新上下文树的GitIgnore解析器
    if (mContextTree) {
        mContextTree->setGitIgnoreRules(rules);
    }

    // 保存到UserOptions
    USER_OP.contextBuilderFilterRules = rules;
}

void ContextBuilderWgt::onIncludeSubfoldersChanged()
{
    if (!mIncludeSubfoldersYes || !mContextTree) {
        return;
    }

    bool includeSubfolders = mIncludeSubfoldersYes->isChecked();
    mContextTree->setIncludeSubfolders(includeSubfolders);

    // 保存到UserOptions
    USER_OP.contextBuilderIncludeSubfolders = includeSubfolders;
}

void ContextBuilderWgt::onResetFilterRules()
{
    if (mFilterRulesEdit) {
        QString defaultRules = CodeBoosterSettings::instance().getDefaultGitIgnoreRules();
        mFilterRulesEdit->setPlainText(defaultRules);
        onFilterRulesChanged();
    }
}

void ContextBuilderWgt::onSourceCodeOnTopChanged()
{
    // 当格式设置改变时，重新构建最终Prompt
    updateFinalPrompt();

    // 保存到UserOptions
    if (mSourceCodeOnTopCheckBox) {
        USER_OP.contextBuilderSourceCodeOnTop = mSourceCodeOnTopCheckBox->isChecked();
    }
}

void ContextBuilderWgt::updateFinalPrompt()
{
    // 保存滚动条位置
    int scrollBarValue = mFinalPromptEdit->verticalScrollBar()->value();

    QString finalPrompt = buildFinalPrompt();
    mFinalPromptEdit->setPlainText(finalPrompt);

    // 恢复滚动条位置
    mFinalPromptEdit->verticalScrollBar()->setValue(scrollBarValue);

    // 更新发送按钮状态
    QList<ContextItem> selectedContexts = mContextTree->getSelectedContextItems();
    QString instruction = mInstructionEdit->toPlainText().trimmed();
    mSendToChatBtn->setEnabled(!selectedContexts.isEmpty() || !instruction.isEmpty());
}

QString ContextBuilderWgt::buildFinalPrompt() const
{
    QString instruction = mInstructionEdit->toPlainText().trimmed();
    QList<ContextItem> selectedContexts = mContextTree->getSelectedContextItems();

    if (instruction.isEmpty() && selectedContexts.isEmpty()) {
        return QString();
    }

    QString finalPrompt;
    bool sourceCodeOnTop = mSourceCodeOnTopCheckBox && mSourceCodeOnTopCheckBox->isChecked();

    if (sourceCodeOnTop) {
        // 源代码在顶部：先添加上下文信息，再添加用户指令
        if (!selectedContexts.isEmpty()) {
            finalPrompt += ContextItem::contextsToString(selectedContexts);
            finalPrompt += "\n";
        }

        if (!instruction.isEmpty()) {
            finalPrompt += "<task-instruction>\n";
            finalPrompt += instruction;
            finalPrompt += "\n</task-instruction>";
        }
    } else {
        // 用户指令在顶部：先添加用户指令，再添加上下文信息
        if (!instruction.isEmpty()) {
            finalPrompt += "<task-instruction>\n";
            finalPrompt += instruction;
            finalPrompt += "\n</task-instruction>\n";

            if (!selectedContexts.isEmpty()) {
                finalPrompt += "\n";
            }
        }

        if (!selectedContexts.isEmpty()) {
            finalPrompt += ContextItem::contextsToString(selectedContexts);
        }
    }

    return finalPrompt;
}

void ContextBuilderWgt::dragEnterEvent(QDragEnterEvent *event)
{
    // 将拖拽事件转发给ContextNavigationTree处理
    if (mContextTree) {
        mContextTree->handleDragEnterEvent(event);
        // 如果事件被接受，显示拖拽遮罩
        // if (event->isAccepted())
        {
            showDragOverlay();
        }
    } else {
        event->ignore();
    }
}

void ContextBuilderWgt::dragMoveEvent(QDragMoveEvent *event)
{
    // 将拖拽事件转发给ContextNavigationTree处理
    if (mContextTree) {
        mContextTree->handleDragMoveEvent(event);
    } else {
        event->ignore();
    }
}

void ContextBuilderWgt::dropEvent(QDropEvent *event)
{
    // 隐藏拖拽遮罩
    hideDragOverlay();

    // 将拖拽事件转发给ContextNavigationTree处理
    if (mContextTree) {
        mContextTree->handleDropEvent(event);
    } else {
        event->ignore();
    }
}

void ContextBuilderWgt::dragLeaveEvent(QDragLeaveEvent *event)
{
    // 隐藏拖拽遮罩
    hideDragOverlay();
    QWidget::dragLeaveEvent(event);
}

void ContextBuilderWgt::createDragOverlay()
{
    mDragOverlay = new DragOverlayWidget(this);
    mDragOverlay->hide();
}

void ContextBuilderWgt::showDragOverlay()
{
    // 修改mContextTree的边框为绿色虚线，保留原有样式
    if (mContextTree) {
        QString currentStyle = mContextTree->styleSheet();
        QString dragStyle = currentStyle + "QTreeWidget { border: 2px dashed green !important; }";
        mContextTree->setStyleSheet(dragStyle);
    }

    // 保留原有遮罩代码用于对比
    if (mDragOverlay) {
        mDragOverlay->setGeometry(rect());
        mDragOverlay->raise();
        // mDragOverlay->show(); // 注释掉，不显示遮罩
    }
}

void ContextBuilderWgt::hideDragOverlay()
{
    // 恢复mContextTree的默认边框样式（2px灰色虚线）
    if (mContextTree) {
        QString currentStyle = mContextTree->styleSheet();
        // 移除拖拽时添加的绿色边框样式
        QString cleanStyle = currentStyle.replace("QTreeWidget { border: 2px dashed green !important; }", "");
        // 设置默认的2px灰色虚线边框
        cleanStyle += "QTreeWidget { border: 2px dashed gray; }";
        mContextTree->setStyleSheet(cleanStyle);
    }

    // 保留原有遮罩代码用于对比
    if (mDragOverlay) {
        mDragOverlay->hide();
    }
}

void ContextNavigationTree::onFilterTextChanged()
{
    QString filterText = mFilterLineEdit->text().trimmed();
    filterTreeItems(filterText);
}

void ContextNavigationTree::filterTreeItems(const QString &filterText)
{
    // 递归过滤函数
    std::function<bool(QTreeWidgetItem*)> filterItem = [&](QTreeWidgetItem* item) -> bool {
        bool shouldShow = false;

        // 检查当前项是否匹配过滤条件
        if (filterText.isEmpty()) {
            shouldShow = true; // 空过滤文本时显示所有项
        } else {
            // 大小写不敏感的文本匹配
            QString itemText = item->text(0).toLower();
            QString filter = filterText.toLower();
            shouldShow = itemText.contains(filter);
        }

        // 递归检查子项
        bool hasVisibleChild = false;
        for (int i = 0; i < item->childCount(); ++i) {
            QTreeWidgetItem* child = item->child(i);
            bool childVisible = filterItem(child);
            if (childVisible) {
                hasVisibleChild = true;
            }
        }

        // 如果有可见的子项，父项也应该可见
        if (hasVisibleChild) {
            shouldShow = true;
        }

        // 设置项的可见性
        item->setHidden(!shouldShow);

        return shouldShow;
    };

    // 对所有顶级项应用过滤
    for (int i = 0; i < topLevelItemCount(); ++i) {
        QTreeWidgetItem* item = topLevelItem(i);
        filterItem(item);
    }
}

void ContextNavigationTree::updateChildrenCheckState(QTreeWidgetItem *parentItem, Qt::CheckState state)
{
    if (!parentItem) {
        return;
    }

    // 递归更新所有子节点的勾选状态
    for (int i = 0; i < parentItem->childCount(); ++i) {
        QTreeWidgetItem *child = parentItem->child(i);
        if (child) {
            child->setCheckState(0, state);
            // 递归更新子节点的子节点
            updateChildrenCheckState(child, state);
        }
    }
}

void ContextNavigationTree::updateParentCheckState(QTreeWidgetItem *childItem)
{
    if (!childItem) {
        return;
    }

    QTreeWidgetItem *parent = childItem->parent();
    if (!parent) {
        return; // 已经是顶级节点
    }

    // 统计父节点下所有子节点的勾选状态
    int checkedCount = 0;
    int partiallyCheckedCount = 0;
    int totalCount = parent->childCount();

    for (int i = 0; i < totalCount; ++i) {
        QTreeWidgetItem *sibling = parent->child(i);
        if (sibling) {
            Qt::CheckState siblingState = sibling->checkState(0);
            if (siblingState == Qt::Checked) {
                checkedCount++;
            } else if (siblingState == Qt::PartiallyChecked) {
                partiallyCheckedCount++;
            }
        }
    }

    // 根据子节点状态设置父节点状态
    Qt::CheckState newParentState;
    if (checkedCount == totalCount) {
        // 所有子节点都被勾选
        newParentState = Qt::Checked;
    } else if (checkedCount == 0 && partiallyCheckedCount == 0) {
        // 所有子节点都未被勾选
        newParentState = Qt::Unchecked;
    } else {
        // 部分子节点被勾选或有部分勾选状态
        newParentState = Qt::PartiallyChecked;
    }

    parent->setCheckState(0, newParentState);

    // 递归更新父节点的父节点
    updateParentCheckState(parent);
}

} // namespace CodeBooster::Internal
