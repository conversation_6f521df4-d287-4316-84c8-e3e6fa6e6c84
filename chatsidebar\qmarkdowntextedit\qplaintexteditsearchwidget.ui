<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QPlainTextEditSearchWidget</class>
 <widget class="QWidget" name="QPlainTextEditSearchWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>836</width>
    <height>142</height>
   </rect>
  </property>
  <property name="autoFillBackground">
   <bool>true</bool>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item row="0" column="3">
    <widget class="QLineEdit" name="searchLineEdit">
     <property name="placeholderText">
      <string>Find in text</string>
     </property>
    </widget>
   </item>
   <item row="1" column="3" colspan="2">
    <widget class="QLineEdit" name="replaceLineEdit">
     <property name="placeholderText">
      <string>Replace with</string>
     </property>
    </widget>
   </item>
   <item row="0" column="4">
    <widget class="QLabel" name="searchCountLabel">
     <property name="text">
      <string notr="true">-/-</string>
     </property>
    </widget>
   </item>
   <item row="0" column="1">
    <widget class="QLabel" name="searchLabel">
     <property name="text">
      <string>Find:</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="0" column="6">
    <widget class="QPushButton" name="searchUpButton">
     <property name="toolTip">
      <string>Search backward</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="icon">
      <iconset theme="go-top" resource="media.qrc">
       <normaloff>:/media/go-top.svg</normaloff>:/media/go-top.svg</iconset>
     </property>
     <property name="flat">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item row="1" column="0" colspan="2">
    <widget class="QLabel" name="replaceLabel">
     <property name="text">
      <string>Replace:</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="0" column="0">
    <widget class="QPushButton" name="closeButton">
     <property name="toolTip">
      <string>Close search</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="icon">
      <iconset theme="window-close" resource="media.qrc">
       <normaloff>:/media/window-close.svg</normaloff>:/media/window-close.svg</iconset>
     </property>
     <property name="flat">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item row="0" column="7">
    <widget class="QPushButton" name="replaceToggleButton">
     <property name="toolTip">
      <string>Advanced search / replace text</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="icon">
      <iconset theme="edit-find-replace" resource="media.qrc">
       <normaloff>:/media/edit-find-replace.svg</normaloff>:/media/edit-find-replace.svg</iconset>
     </property>
     <property name="checkable">
      <bool>true</bool>
     </property>
     <property name="flat">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item row="0" column="5">
    <widget class="QPushButton" name="searchDownButton">
     <property name="toolTip">
      <string>Search forward</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="icon">
      <iconset theme="go-bottom" resource="media.qrc">
       <normaloff>:/media/go-bottom.svg</normaloff>:/media/go-bottom.svg</iconset>
     </property>
     <property name="flat">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item row="1" column="5">
    <widget class="QPushButton" name="matchCaseSensitiveButton">
     <property name="toolTip">
      <string>Match case sensitive</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="icon">
      <iconset theme="format-text-superscript" resource="media.qrc">
       <normaloff>:/media/format-text-superscript.svg</normaloff>:/media/format-text-superscript.svg</iconset>
     </property>
     <property name="checkable">
      <bool>true</bool>
     </property>
     <property name="flat">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item row="2" column="0" colspan="2">
    <widget class="QLabel" name="modeLabel">
     <property name="text">
      <string>Mode:</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item row="2" column="3" colspan="2">
    <widget class="QFrame" name="buttonFrame">
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QComboBox" name="modeComboBox">
        <item>
         <property name="text">
          <string>Plain text</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>Whole words</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>Regular expression</string>
         </property>
        </item>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="replaceButton">
        <property name="toolTip">
         <string>Replace one text occurrence</string>
        </property>
        <property name="text">
         <string>Replace</string>
        </property>
        <property name="flat">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="replaceAllButton">
        <property name="toolTip">
         <string>Replace all text occurrences</string>
        </property>
        <property name="text">
         <string>Replace all</string>
        </property>
        <property name="flat">
         <bool>false</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <tabstops>
  <tabstop>searchLineEdit</tabstop>
  <tabstop>replaceLineEdit</tabstop>
  <tabstop>replaceButton</tabstop>
  <tabstop>replaceAllButton</tabstop>
  <tabstop>searchDownButton</tabstop>
  <tabstop>searchUpButton</tabstop>
  <tabstop>replaceToggleButton</tabstop>
  <tabstop>closeButton</tabstop>
 </tabstops>
 <resources>
  <include location="media.qrc"/>
 </resources>
 <connections/>
</ui>
