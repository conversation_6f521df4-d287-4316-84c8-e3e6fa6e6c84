# 上下文导航树结构显示功能

## 问题描述

之前的上下文导航树只显示顶级项目（文件夹和文件），没有展示文件夹的内部结构。用户无法看到文件夹内部的目录和文件层级关系，这降低了用户体验。

## 解决方案

实现了完整的树形结构显示功能，当启用"读取子文件夹"选项时，文件夹内的所有目录和文件都会按照层级结构展示出来。

## 主要改进

### 1. 新增 `buildFolderStructure` 方法

```cpp
void ContextNavigationTree::buildFolderStructure(ContextTreeItem *parentItem, const QString &folderPath)
```

**功能**：
- 递归遍历文件夹内容
- 应用GitIgnore过滤规则
- 按照目录优先、文件其次的顺序排列
- 创建树形节点结构

**实现逻辑**：
1. 扫描指定文件夹的所有内容
2. 分离目录和文件，分别排序
3. 先创建目录节点，再创建文件节点
4. 对每个目录递归调用自身

### 2. 增强 `addContextItem` 方法

**新增功能**：
- 检测文件夹类型的上下文项
- 在启用子文件夹时自动构建内部结构
- 默认展开文件夹节点

**代码示例**：
```cpp
// 如果是文件夹且启用了子文件夹，构建子项结构
if (context.type == ContextItem::Folder && mIncludeSubfolders) {
    buildFolderStructure(newItem, context.uri);
    newItem->setExpanded(true); // 默认展开
}
```

### 3. 改进 `setIncludeSubfolders` 方法

**新增功能**：
- 动态切换子文件夹显示模式
- 实时重建树形结构
- 避免重复操作

**实现逻辑**：
```cpp
void ContextNavigationTree::setIncludeSubfolders(bool include)
{
    if (mIncludeSubfolders == include) {
        return; // 没有变化，直接返回
    }
    
    mIncludeSubfolders = include;
    
    // 重新构建所有文件夹的子项结构
    for (int i = 0; i < topLevelItemCount(); ++i) {
        ContextTreeItem *item = dynamic_cast<ContextTreeItem*>(topLevelItem(i));
        if (item && item->contextItem().type == ContextItem::Folder) {
            item->takeChildren(); // 清除现有子项
            
            if (mIncludeSubfolders) {
                buildFolderStructure(item, item->contextItem().uri);
                item->setExpanded(true);
            }
        }
    }
}
```

### 4. 增强选择操作方法

**改进的方法**：
- `selectAll()` - 全选所有项目（包括子项）
- `selectNone()` - 取消选择所有项目
- `invertSelection()` - 反选所有项目

**技术实现**：
使用递归lambda函数处理树形结构：
```cpp
std::function<void(QTreeWidgetItem*)> selectAllRecursive = [&](QTreeWidgetItem* item) {
    ContextTreeItem *contextItem = dynamic_cast<ContextTreeItem*>(item);
    if (contextItem) {
        contextItem->setChecked(true);
    }
    
    for (int i = 0; i < item->childCount(); ++i) {
        selectAllRecursive(item->child(i));
    }
};
```

### 5. 改进 `getSelectedContextItems` 方法

**新增功能**：
- 递归收集所有选中的项目
- 为子项文件动态读取内容
- 区分顶级项和子项的处理方式

**关键改进**：
```cpp
// 对于子项，需要读取实际的文件内容
if (item->parent() != nullptr && context.type == ContextItem::File) {
    bool success = false;
    QString fileContent = Internal::readTextFile(context.uri, success);
    if (success) {
        context.content = fileContent;
    }
}
```

### 6. 增强刷新功能

**改进的 `refreshContextItems` 方法**：
- 重新构建文件夹的树形结构
- 保持展开状态
- 更新文件内容

## 用户体验改进

### 1. 直观的层级显示
```
📁 testFolder
├── 📁 build
│   ├── 📄 main.o
│   └── 📄 test.o
├── 📁 src
│   ├── 📄 main.cpp
│   └── 📄 test.h
└── 📄 README.md
```

### 2. 智能过滤
- 应用GitIgnore规则到所有层级
- 自动过滤非文本文件
- 尊重用户的过滤设置

### 3. 灵活的选择
- 可以选择整个文件夹
- 可以选择特定的子文件
- 支持批量操作

### 4. 实时更新
- 切换子文件夹选项时立即生效
- 刷新时重新扫描文件夹内容
- 保持用户的选择状态

## 技术细节

### 性能优化
- 使用递归但避免深度过大的文件夹
- 延迟加载文件内容（仅在选中时读取）
- 智能的重建策略

### 内存管理
- 正确管理QTreeWidgetItem的生命周期
- 使用takeChildren()安全清理子项
- 避免内存泄漏

### 错误处理
- 检查文件和目录的存在性
- 处理权限不足的情况
- 优雅处理读取失败

## 使用场景

1. **项目结构浏览**：查看完整的项目文件结构
2. **选择性包含**：只选择需要的特定文件
3. **快速定位**：通过树形结构快速找到目标文件
4. **批量操作**：对整个文件夹或特定文件进行操作

## 兼容性

- 完全向后兼容现有功能
- 不影响单文件的拖拽操作
- 保持原有的过滤和选择逻辑
- 与现有的上下文构建器无缝集成
