#ifndef CONTEXTBUILDERWGT_H
#define CONTEXTBUILDERWGT_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QTextEdit>
#include <QPlainTextEdit>
#include <QPushButton>
#include <QLabel>
#include <QGroupBox>
#include <QTimer>
#include <QDragEnterEvent>
#include <QDragMoveEvent>
#include <QDropEvent>
#include <QDragLeaveEvent>
#include <QMimeData>
#include <QUrl>
#include <QFileInfo>
#include <QDir>
#include <QLineEdit>
#include <QRadioButton>
#include <QToolBar>
#include <QAction>
#include <QToolButton>
#include <QTabWidget>
#include <QTableWidget>
#include <QPainter>
#include <QPixmap>
#include <QCheckBox>

#include <solutions/spinner/spinner.h>
#include "chatcontext/contextitem.h"
#include "contextinstruction.h"

namespace CodeBooster::Internal {

class GitIgnoreParser;
class ContextNavigationTree;

/**
 * @brief 拖拽遮罩层widget，显示拖拽提示
 */
class DragOverlayWidget : public QWidget
{
    Q_OBJECT

public:
    explicit DragOverlayWidget(QWidget *parent = nullptr);

protected:
    void paintEvent(QPaintEvent *event) override;
};

/**
 * @brief 上下文构建器主控件
 */
class ContextBuilderWgt : public QWidget
{
    Q_OBJECT

public:
    explicit ContextBuilderWgt(QWidget *parent = nullptr);
    ~ContextBuilderWgt();

    void addContextItem(const ContextItem &context);
    void clearContextItems();

private slots:
    void onInstructionTextChanged();
    void onUpdatePromptTimer();
    void onSendToChat();
    void onCopyPrompt();
    void onContextItemsChanged();
    void onFilterRulesChanged();
    void onIncludeSubfoldersChanged();
    void onResetFilterRules();
    void onSourceCodeOnTopChanged();
    void onNewInstruction();

protected:
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dragMoveEvent(QDragMoveEvent *event) override;
    void dropEvent(QDropEvent *event) override;
    void dragLeaveEvent(QDragLeaveEvent *event) override;

private:
    void setupUI();
    void setupContextTab();
    void setupOptionsTab();
    void setupInstructionTabs();
    void setupConnections();
    void updateFinalPrompt();
    QString buildFinalPrompt() const;

    // 拖拽遮罩相关方法
    void showDragOverlay();
    void hideDragOverlay();
    void createDragOverlay();
    void loadInstructions();



signals:
    void sendContextToChat(const QList<ContextItem> &contexts);

private:
    // UI 组件
    QVBoxLayout *mMainLayout;
    QTabWidget *mTabWidget;

    // 上下文Tab
    QWidget *mContextTab;

    // 选项Tab
    QWidget *mOptionsTab;
    QGroupBox *mFilterGroupBox;
    QRadioButton *mIncludeSubfoldersYes;
    QRadioButton *mIncludeSubfoldersNo;
    QTextEdit *mFilterRulesEdit;
    QPushButton *mResetFilterRulesBtn;
    QPushButton *mApplyFilterRulesBtn;

    // 格式设置组
    QGroupBox *mFormatGroupBox;
    QCheckBox *mSourceCodeOnTopCheckBox;

    // 上下文导航树区域
    QGroupBox *mContextGroupBox;
    ContextNavigationTree *mContextTree;

    // 指令输入区域
    QTabWidget *mInstructionTabWidget;
    QWidget *mInstructionInputTab;
    QWidget *mInstructionManagementTab;
    QTextEdit *mInstructionEdit;
    QTableWidget *mInstructionTable;
    QPushButton *mNewInstructionBtn;
    QPushButton *mEditInstructionBtn;
    QPushButton *mDeleteInstructionBtn;
    QPushButton *mUseInstructionBtn;

    // 最终Prompt框
    QGroupBox *mPromptGroupBox;
    QPlainTextEdit *mFinalPromptEdit;
    QPushButton *mCopyPromptBtn;

    // 操作按钮
    QPushButton *mSendToChatBtn;

    // 定时器
    QTimer *mUpdatePromptTimer;

    // 其他
    GitIgnoreParser *mGitIgnoreParser;

    // 拖拽遮罩层
    DragOverlayWidget *mDragOverlay;

    // Spinner加载指示器
    SpinnerSolution::Spinner *mSpinner;

    QList<ContextInstruction> mInstructions;

    static const int UPDATE_PROMPT_TIMEOUT_MS;
};

} // namespace CodeBooster::Internal

#endif // CONTEXTBUILDERWGT_H
