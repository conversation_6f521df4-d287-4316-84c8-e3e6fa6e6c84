#include "chatviewfactory.h"

#include <QDialog>
#include <QToolButton>

#include <coreplugin/actionmanager/command.h>
#include <coreplugin/inavigationwidgetfactory.h>
#include <coreplugin/navigationwidget.h>
#include <utils/stylehelper.h>

#include "codeboosterconstants.h"
#include "codeboostertr.h"
#include "common/codeboostericons.h"
#include "chatview.h"

using namespace Core;

namespace CodeBooster::Internal {

ChatViewFactory::ChatViewFactory() :
    parallelChat(false)
{
    setDisplayName(Tr::tr("Code Booster"));
    setPriority(500);
    setId(Constants::CODEBOOSTER_CHAT_VIEW_ID);
    setActivationSequence(QKeySequence(useMacShortcuts ? Tr::tr("Meta+L") : Tr::tr("Alt+L")));
}

ChatViewFactory &ChatViewFactory::instance()
{
    static ChatViewFactory instance;
    return instance;
}

ChatViewFactory::~ChatViewFactory()
{
    // 析构对话框
}

NavigationView ChatViewFactory::createWidget()
{
    qDebug() << "----" << Q_FUNC_INFO;

    // m_view = new ChatView();
    // return {m_view, m_view->createToolButtons()};

    QPointer<ChatViewGroup> viewGroup = new ChatViewGroup;
    mViewGroups << viewGroup;
    connect(viewGroup, &ChatViewGroup::needDestroy, this, [this, viewGroup]()
    {
        mViewGroups.removeAll(viewGroup);
        viewGroup->deleteLater();
    });

    // 同步按钮状态
    connect(viewGroup, &ChatViewGroup::parallelChatActionTriggered, this, [this, viewGroup](bool checked){
        parallelChat = checked;
        for (auto vg : mViewGroups)
        {
            if (vg == viewGroup)
                continue;
            vg->setParallelButtonCheckState(checked);
        }
    });

    connect(viewGroup, &ChatViewGroup::toSendUserMessage, this, [this, viewGroup](const QString &message, const QList<ContextItem> &contexts){
        if (parallelChat)
        {
            for (auto vg : mViewGroups)
            {
                if (vg == viewGroup)
                    continue;
                vg->chatView()->onSendUserMessage(message, contexts);
            }
        }

    });

    return {viewGroup->viewContainer(), viewGroup->toolButtons()};
}


// void setupChatViewWidgetFactory()
// {
//     static ChatViewFactory theChatViewFactory;
// }


// -------------------------------------------------------------------------
// ChatViewGroup
// -------------------------------------------------------------------------
ChatViewGroup::ChatViewGroup() : QObject()
{
    // container的所有权至始至终都是侧边栏控件，或许在ChatViewFactory中创建更合适，为了方便放这了
    container = new QWidget();
    connect(container, &QWidget::destroyed, this, [this](){
        emit needDestroy();
    });

    {
        // ChatView的父控件一直都是container，跟随container析构
        view = new ChatView(container);
        connect(view, &ChatView::toSendUserMessage, this, &ChatViewGroup::toSendUserMessage);
    }

    {
        QVBoxLayout *ly = new QVBoxLayout(container);
        ly->setSpacing(0);
        ly->setContentsMargins(0, 0, 0, 0);
        ly->addWidget(view);

        undockViewBtn = new QPushButton(DOCK_ICON.icon(), "重置视图", seperateDialog);
        undockViewBtn->setMinimumHeight(36);
        undockViewBtn->setVisible(false);
        connect(undockViewBtn, &QPushButton::clicked, this, [this](){
            seperateWindowAction->trigger();
        });

        ly->addWidget(undockViewBtn);
    }

    {
        seperateDialog = new QDialog();
        seperateDialog->setWindowFlags(Qt::Dialog | Qt::WindowMaximizeButtonHint | Qt::WindowCloseButtonHint);
        seperateDialog->setModal(false);
        seperateDialog->setAttribute(Qt::WA_DeleteOnClose, false);
        seperateDialog->setWindowTitle("CodeBooster");
        seperateDialog->setWindowIcon(CODEBOOSTER_ICON.icon());
        connect(seperateDialog, &QDialog::finished, this, [=](int result){
            Q_UNUSED(result)
            container->layout()->addWidget(view);
            undockViewBtn->setVisible(false);
            if (seperateWindowAction->isChecked())
                seperateWindowAction->setChecked(false);
        });

        QVBoxLayout *ly = new QVBoxLayout(seperateDialog);
        ly->setContentsMargins(0, 0, 0, 0);
        ly->setSpacing(0);
    }

    {
        seperateWindowAction = new QAction(DOCK_ICON.icon(), "分离窗口");
        seperateWindowAction->setCheckable(true);
        seperateWindowAction->setChecked(false);
        connect(seperateWindowAction, &QAction::triggered, this, [this](bool checked){
            if (checked)
            {
                seperateDialog->layout()->addWidget(view);
                seperateDialog->show();
                seperateDialog->resize(QSize(350, 800));
                undockViewBtn->setVisible(true);
            }
            else
            {
                seperateDialog->close();
            }
        });

        seperateWindowButton = new QToolButton;
        seperateWindowButton->setDefaultAction(seperateWindowAction);
        seperateWindowButton->setProperty(Utils::StyleHelper::C_NO_ARROW, true);
    }

    {
        parallelChatAction = new QAction(PARALLEL_ICON.icon(), "并行对话");
        parallelChatAction->setCheckable(true);
        parallelChatAction->setChecked(false);
        connect(parallelChatAction, &QAction::triggered, this, [this](bool checked){
            emit parallelChatActionTriggered(checked);
        });

        parallelChatButton = new QToolButton;
        parallelChatButton->setDefaultAction(parallelChatAction);
        parallelChatButton->setProperty(Utils::StyleHelper::C_NO_ARROW, true);
    }
}

ChatViewGroup::~ChatViewGroup()
{
    seperateDialog->deleteLater();
    seperateWindowAction->deleteLater();
    seperateWindowButton->deleteLater();

    parallelChatAction->deleteLater();
    parallelChatButton->deleteLater();
}

QList<QToolButton *> ChatViewGroup::toolButtons()
{
    QList<QToolButton *> buttons;
    buttons << view->createToolButtons();
    buttons << parallelChatButton;
    buttons << seperateWindowButton;

    return buttons;
}

QWidget *ChatViewGroup::viewContainer() const
{
    return container;
}

ChatView *ChatViewGroup::chatView() const
{
    return view.get();
}

void ChatViewGroup::setParallelButtonCheckState(bool checked)
{
    parallelChatAction->setChecked(checked);
}

}
