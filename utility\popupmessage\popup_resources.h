#ifndef POPUP_RESOURCES_H
#define POPUP_RESOURCES_H

// 自动生成的图标资源文件
// 请勿手动编辑此文件

const unsigned char popup_correct_data[] = {
    0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d, 0x49, 0x48, 0x44, 0x52, 
    0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x30, 0x08, 0x06, 0x00, 0x00, 0x00, 0x57, 0x02, 0xf9, 
    0x87, 0x00, 0x00, 0x00, 0x01, 0x73, 0x52, 0x47, 0x42, 0x00, 0xae, 0xce, 0x1c, 0xe9, 0x00, 0x00, 
    0x03, 0x89, 0x49, 0x44, 0x41, 0x54, 0x68, 0x43, 0xcd, 0x5a, 0x8b, 0x71, 0xdb, 0x30, 0x0c, 0x25, 
    0x26, 0x69, 0x33, 0x49, 0x9b, 0x49, 0x5a, 0x4f, 0x52, 0x67, 0x92, 0x7a, 0x93, 0xba, 0x93, 0xd4, 
    0x99, 0x84, 0xf5, 0xd3, 0x11, 0x3a, 0x08, 0x06, 0x08, 0x92, 0xa2, 0x75, 0xd6, 0x5d, 0x2e, 0x1f, 
    0x2b, 0xe4, 0x7b, 0xc4, 0xc3, 0x87, 0x04, 0x29, 0x4d, 0x7a, 0x72, 0xce, 0xdf, 0x53, 0x4a, 0xdf, 
    0x52, 0x4a, 0x5f, 0xd5, 0xd7, 0xad, 0x4c, 0x81, 0xef, 0xfc, 0xf3, 0x5f, 0x22, 0xba, 0xcc, 0x98, 
    0x9a, 0x46, 0x07, 0xc9, 0x39, 0x03, 0xe8, 0x8f, 0x02, 0xf6, 0xe7, 0xc0, 0x38, 0x20, 0x73, 0x4d, 
    0x29, 0xed, 0x22, 0x33, 0x44, 0x20, 0xe7, 0xfc, 0xeb, 0x0e, 0xf8, 0x3c, 0x00, 0xda, 0xfb, 0x17, 
    0x90, 0xb9, 0x10, 0xd1, 0x47, 0xef, 0x98, 0x5d, 0x04, 0x1a, 0x80, 0xb3, 0x4c, 0xa4, 0x5c, 0x3e, 
    0x53, 0x4a, 0x5f, 0x0c, 0x69, 0x59, 0x58, 0xbb, 0x89, 0x34, 0x11, 0x28, 0x72, 0xf9, 0x7d, 0x37, 
    0x39, 0x74, 0xae, 0x9f, 0x65, 0xd2, 0x22, 0x05, 0x48, 0x22, 0x7c, 0x84, 0xfc, 0x30, 0x9e, 0x37, 
    0xe6, 0x3b, 0x11, 0xb1, 0xcf, 0xb8, 0x63, 0x86, 0x04, 0x8a, 0x73, 0xfe, 0x71, 0x80, 0x9f, 0x88, 
    0xa8, 0x09, 0xb4, 0x87, 0xa0, 0xb2, 0x38, 0x00, 0x1f, 0x92, 0xa8, 0x12, 0x70, 0xc0, 0x77, 0x9b, 
    0x39, 0x34, 0x49, 0x4a, 0x29, 0xe7, 0x8c, 0x40, 0x00, 0xdf, 0x42, 0x70, 0xe0, 0x27, 0x9c, 0xcb, 
    0x25, 0xe0, 0x80, 0xbf, 0x12, 0xd1, 0x7b, 0x0b, 0xa0, 0x91, 0x77, 0x84, 0xb4, 0x74, 0x80, 0x38, 
    0x7b, 0x0e, 0x6e, 0x12, 0x70, 0xc0, 0xbb, 0x83, 0x8c, 0x80, 0x0d, 0x24, 0x85, 0xf0, 0x2c, 0x49, 
    0xc0, 0x12, 0xa6, 0x5c, 0x1f, 0x08, 0x94, 0x55, 0xf8, 0xa7, 0x26, 0x80, 0x16, 0x77, 0x69, 0xbd, 
    0x97, 0xa4, 0x11, 0xf1, 0x4c, 0x9f, 0xb0, 0x08, 0xc0, 0x61, 0x65, 0x64, 0x38, 0x64, 0xe5, 0x2d, 
    0x82, 0x16, 0x09, 0x22, 0x7a, 0x93, 0xef, 0x6e, 0x08, 0x14, 0x47, 0x42, 0xb8, 0xe4, 0xe7, 0xa9, 
    0x9a, 0x8f, 0xac, 0x52, 0xd4, 0x00, 0xc7, 0x96, 0x99, 0x1e, 0x52, 0x5a, 0xcb, 0x10, 0x4d, 0x00, 
    0xd2, 0xe1, 0x28, 0x70, 0xd3, 0x6c, 0xa3, 0x09, 0x9f, 0xf1, 0xb9, 0x11, 0x66, 0x37, 0xb8, 0x56, 
    0x02, 0x86, 0xb9, 0x0e, 0xd7, 0x7d, 0xc5, 0xb1, 0x61, 0x01, 0xa9, 0x0c, 0x94, 0x1d, 0x27, 0xbc, 
    0x2f, 0x09, 0xe4, 0x57, 0x91, 0x8e, 0xe3, 0x0f, 0xd2, 0x37, 0x57, 0x2b, 0x2c, 0x04, 0x0c, 0xed, 
    0xbf, 0xcc, 0xea, 0x33, 0x19, 0x23, 0xb4, 0x2f, 0x18, 0x99, 0x00, 0xcc, 0xb3, 0x3a, 0xca, 0x3d, 
    0x69, 0x84, 0x25, 0xc6, 0x2c, 0xbd, 0x0b, 0x8d, 0x63, 0xc8, 0x8f, 0x5a, 0xb8, 0xce, 0x39, 0x4b, 
    0x2b, 0x2c, 0x01, 0x86, 0x09, 0x48, 0xf9, 0xac, 0xfa, 0x9a, 0x05, 0x32, 0x48, 0x5a, 0x32, 0xe7, 
    0x54, 0xa3, 0x9e, 0xf2, 0xd3, 0x45, 0x46, 0x64, 0xc8, 0xe7, 0xad, 0xa5, 0x0a, 0xdc, 0x4b, 0xce, 
    0x49, 0x98, 0xd5, 0x9c, 0x63, 0xca, 0x28, 0xe7, 0x7c, 0xb8, 0x7c, 0x1c, 0xf0, 0x4d, 0x39, 0x47, 
    0xc9, 0xe8, 0x0c, 0x0b, 0x48, 0x02, 0x4f, 0x97, 0xcf, 0x1e, 0xf0, 0x25, 0xe0, 0x6c, 0xfd, 0x40, 
    0x31, 0x0a, 0x09, 0x88, 0xb2, 0x97, 0x0b, 0xac, 0x70, 0xd3, 0x21, 0x22, 0x09, 0x92, 0xa4, 0xae, 
    0xb3, 0x9a, 0x56, 0x5e, 0x8c, 0x21, 0x17, 0xfc, 0x0a, 0x0b, 0xc8, 0xec, 0x1b, 0xd6, 0x3d, 0x8a, 
    0x30, 0xc6, 0x6d, 0xf2, 0x99, 0xbd, 0x2b, 0x2f, 0x08, 0xc8, 0xfd, 0xf8, 0x0d, 0x04, 0x64, 0x04, 
    0xda, 0xd4, 0x19, 0x8d, 0x05, 0x56, 0x48, 0x62, 0x16, 0xf8, 0x22, 0xa1, 0x2a, 0x81, 0xd0, 0x02, 
    0x65, 0x90, 0x8d, 0xe3, 0x17, 0xa2, 0xa6, 0x25, 0x66, 0x82, 0xb7, 0x92, 0x6e, 0xb7, 0x84, 0x1c, 
    0x2d, 0xf2, 0x9f, 0x37, 0x24, 0x66, 0x83, 0xf7, 0x2c, 0x20, 0xbd, 0x3a, 0x74, 0x62, 0x29, 0x2b, 
    0x1d, 0x82, 0xa5, 0x25, 0x9e, 0x01, 0xde, 0xb0, 0xfe, 0xe2, 0xc4, 0x5b, 0xaf, 0xee, 0xdc, 0xf3, 
    0x3a, 0x24, 0xb0, 0x6f, 0xd6, 0x27, 0x19, 0x5d, 0xd1, 0xa6, 0x92, 0xbd, 0xb7, 0x0b, 0x6e, 0xa5, 
    0xe7, 0xde, 0x2c, 0xeb, 0x90, 0x90, 0xc3, 0x4c, 0x01, 0x5f, 0x2c, 0x20, 0xa3, 0xe6, 0x05, 0x16, 
    0xc0, 0xf6, 0x51, 0xae, 0xd6, 0x50, 0x25, 0x5a, 0x21, 0x31, 0x13, 0xbc, 0xde, 0x17, 0x9c, 0x40, 
    0x40, 0x27, 0x97, 0xa6, 0x48, 0xe4, 0x84, 0x58, 0x1d, 0x9d, 0xa6, 0x81, 0xb7, 0xa2, 0x1f, 0xaa, 
    0x66, 0xae, 0x46, 0xcd, 0xcd, 0x42, 0xaf, 0x94, 0xc4, 0x24, 0xb0, 0x2a, 0xc0, 0x2f, 0xbb, 0xa6, 
    0x59, 0x8f, 0x4a, 0xba, 0x4b, 0xc0, 0x61, 0x02, 0x53, 0x64, 0x34, 0x0b, 0xa8, 0x63, 0x5d, 0x8d, 
    0x71, 0x49, 0xba, 0x4c, 0x40, 0xcb, 0xe8, 0x25, 0x36, 0xf4, 0x2a, 0x64, 0x6f, 0x8e, 0x7b, 0x58, 
    0x3d, 0x72, 0x4f, 0xfc, 0xe8, 0x20, 0x93, 0xba, 0x28, 0x7b, 0x2d, 0x63, 0xec, 0x59, 0x56, 0x3f, 
    0x95, 0x04, 0x60, 0x05, 0xb0, 0x7c, 0xa9, 0x63, 0x15, 0x23, 0x74, 0x26, 0xb9, 0xe5, 0x8d, 0x0e, 
    0xb6, 0xba, 0x32, 0xf3, 0xde, 0x95, 0x76, 0xb4, 0xaf, 0x4f, 0x0a, 0xab, 0x07, 0x5b, 0x58, 0x7d, 
    0xdd, 0xc8, 0x18, 0x0e, 0xab, 0x7b, 0x09, 0x19, 0x67, 0x55, 0x0f, 0x61, 0xd9, 0x3b, 0xdc, 0x95, 
    0x52, 0x02, 0x8e, 0xc3, 0x49, 0x18, 0x09, 0xd6, 0x0c, 0x2c, 0xde, 0xf1, 0xba, 0xb5, 0x73, 0x3a, 
    0x8c, 0x84, 0xd3, 0x8b, 0x33, 0x2b, 0x84, 0x5a, 0x83, 0x43, 0x47, 0x25, 0x58, 0x62, 0x6a, 0x66, 
    0x6d, 0xd4, 0x3c, 0x5e, 0x73, 0xcb, 0x9b, 0xa8, 0xc5, 0x64, 0xb5, 0x53, 0xc3, 0xb6, 0xcf, 0x88, 
    0xf6, 0x9d, 0x16, 0x53, 0x15, 0x3c, 0x3e, 0x0c, 0x4f, 0xe0, 0x4a, 0xad, 0xa4, 0x7d, 0x02, 0xff, 
    0xeb, 0x76, 0x4d, 0x7a, 0x08, 0xec, 0x1d, 0x3f, 0x24, 0x50, 0xe2, 0xb0, 0xce, 0x11, 0x12, 0xe3, 
    0x9e, 0x36, 0x2b, 0x64, 0x2a, 0x9b, 0x7a, 0x3c, 0x6e, 0x73, 0x25, 0xd0, 0x44, 0x40, 0x90, 0x40, 
    0xef, 0xca, 0x9b, 0x94, 0xad, 0xb2, 0xb9, 0x13, 0x51, 0x9a, 0xdc, 0xf8, 0x8c, 0xef, 0x50, 0x58, 
    0x7d, 0xe1, 0x15, 0x78, 0x6f, 0xc7, 0xbe, 0x99, 0x00, 0xcf, 0x50, 0xe9, 0x24, 0xf6, 0x28, 0x47, 
    0xbf, 0x3b, 0xec, 0x57, 0xdd, 0x04, 0x14, 0x11, 0xbe, 0xa1, 0x32, 0x7a, 0xd9, 0x63, 0xb9, 0xf0, 
    0x31, 0x72, 0x47, 0x82, 0x71, 0x0c, 0x13, 0x50, 0x95, 0x22, 0xe4, 0xc1, 0x64, 0xa4, 0x5c, 0xf0, 
    0x77, 0x7d, 0xdd, 0x06, 0xbf, 0x7f, 0xce, 0xba, 0x6e, 0xf3, 0x1f, 0x07, 0xc0, 0x44, 0x61, 0xc3, 
    0x0b, 0x7e, 0x14, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
const unsigned int popup_correct_data_size = 975;

const unsigned char popup_error_data[] = {
    0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d, 0x49, 0x48, 0x44, 0x52, 
    0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x30, 0x08, 0x06, 0x00, 0x00, 0x00, 0x57, 0x02, 0xf9, 
    0x87, 0x00, 0x00, 0x00, 0x01, 0x73, 0x52, 0x47, 0x42, 0x00, 0xae, 0xce, 0x1c, 0xe9, 0x00, 0x00, 
    0x03, 0x2e, 0x49, 0x44, 0x41, 0x54, 0x68, 0x43, 0xed, 0x59, 0x8b, 0x4d, 0x24, 0x31, 0x0c, 0x4d, 
    0x3a, 0x39, 0x2a, 0x39, 0xa8, 0x04, 0xa8, 0xe4, 0xa0, 0x92, 0x83, 0x4a, 0x80, 0x4a, 0xd8, 0x4e, 
    0x7c, 0xfb, 0x56, 0x19, 0x64, 0x3c, 0xcf, 0x89, 0x9d, 0x89, 0x74, 0x5a, 0x89, 0x91, 0xd0, 0xae, 
    0x96, 0x7c, 0xfc, 0xfc, 0x79, 0xfe, 0x4c, 0x2d, 0x57, 0xfe, 0xd4, 0x2b, 0x97, 0xbf, 0xfc, 0x00, 
    0xf8, 0xdf, 0x16, 0x5c, 0x6e, 0x01, 0x11, 0xf9, 0xd5, 0x40, 0xe1, 0x73, 0xfb, 0x7e, 0xc2, 0x6f, 
    0xb5, 0xd6, 0xf7, 0xd5, 0x80, 0x97, 0x00, 0x68, 0x42, 0xdf, 0x97, 0x52, 0x1e, 0x94, 0xd0, 0x9e, 
    0xac, 0x00, 0x03, 0x20, 0xaf, 0x2b, 0x00, 0x1d, 0x02, 0x20, 0x22, 0x7f, 0xce, 0x52, 0x3e, 0x1d, 
    0xd0, 0x2a, 0xc0, 0xbc, 0xd4, 0x5a, 0x9f, 0x67, 0xcf, 0x98, 0x02, 0x20, 0x22, 0xb7, 0xa5, 0x94, 
    0xbf, 0x01, 0x6d, 0x47, 0xe5, 0x02, 0x90, 0xe7, 0x5a, 0xeb, 0x4b, 0x74, 0xc3, 0xb6, 0x2e, 0x0d, 
    0x40, 0x44, 0x20, 0x38, 0x5c, 0x85, 0x3d, 0x17, 0x8d, 0x96, 0x52, 0x3e, 0xce, 0x6b, 0x4e, 0xb5, 
    0xd6, 0x13, 0x89, 0x09, 0xb8, 0x1a, 0x14, 0xc0, 0x9e, 0xa7, 0xac, 0x35, 0xc2, 0x00, 0x9a, 0x20, 
    0x10, 0xde, 0x5e, 0x9e, 0x76, 0x83, 0x41, 0xcc, 0xe0, 0xbc, 0x3b, 0x80, 0x8f, 0x58, 0x23, 0x03, 
    0xe0, 0x8d, 0x08, 0x9f, 0xd6, 0x98, 0x16, 0x4a, 0x01, 0xb1, 0x71, 0x14, 0x06, 0x11, 0x02, 0x20, 
    0x22, 0x56, 0x78, 0x5c, 0xf0, 0xb8, 0x82, 0x45, 0x00, 0xa8, 0x01, 0xc1, 0x1d, 0x1b, 0xed, 0xe2, 
    0xe7, 0xf7, 0x5a, 0xeb, 0xdd, 0xc8, 0x0a, 0x43, 0x00, 0xc4, 0xe7, 0xe1, 0xdb, 0x37, 0xa3, 0x83, 
    0xb3, 0xff, 0x77, 0x40, 0x80, 0xa1, 0x1e, 0x7b, 0x67, 0x75, 0x01, 0x34, 0xb6, 0x81, 0x66, 0xb6, 
    0x27, 0x6c, 0xda, 0x2c, 0x80, 0x8e, 0x25, 0x10, 0x0f, 0x6e, 0x02, 0x1c, 0x01, 0xb0, 0xae, 0xd3, 
    0x3d, 0x6c, 0x46, 0x68, 0xbb, 0xa7, 0x59, 0xe2, 0x53, 0x2b, 0xad, 0x67, 0x71, 0x17, 0x80, 0x88, 
    0x80, 0x2a, 0xc1, 0x3a, 0xdb, 0xe3, 0x06, 0xec, 0x46, 0x95, 0x51, 0xe6, 0x18, 0xad, 0x27, 0x77, 
    0x23, 0xde, 0x68, 0x8e, 0xe8, 0x01, 0x80, 0x16, 0xbe, 0x82, 0xea, 0xcc, 0xcf, 0x74, 0xad, 0xd2, 
    0x58, 0xc8, 0xbd, 0x14, 0x1d, 0xe3, 0x6c, 0x4a, 0x97, 0x24, 0x1e, 0xdc, 0xb8, 0xf3, 0x84, 0xb2, 
    0xda, 0xa7, 0x1a, 0x60, 0xe6, 0xee, 0x71, 0x38, 0xc9, 0x25, 0x2e, 0x68, 0x62, 0x05, 0xea, 0xbe, 
    0x1e, 0x80, 0x6f, 0xd9, 0x76, 0xa0, 0x7d, 0x4b, 0x7f, 0x54, 0x28, 0x27, 0x11, 0xba, 0x9a, 0x25, 
    0xca, 0xa1, 0x2e, 0xec, 0x01, 0xd0, 0xee, 0xd3, 0xa5, 0x32, 0x87, 0xfe, 0xbe, 0x81, 0xc8, 0x0a, 
    0xbf, 0x05, 0x9d, 0xc9, 0x3f, 0x14, 0xac, 0x07, 0x40, 0x54, 0xf0, 0xba, 0x01, 0xa4, 0x2e, 0x82, 
    0x3f, 0x53, 0x4b, 0xb4, 0x35, 0xb6, 0x04, 0x09, 0xe5, 0x12, 0xe2, 0x46, 0x37, 0x96, 0x28, 0x76, 
    0x00, 0x88, 0xe9, 0x42, 0xd4, 0xd9, 0xb1, 0x04, 0xac, 0xa1, 0xeb, 0xa7, 0x90, 0xf0, 0x2d, 0x2f, 
    0x60, 0x9f, 0xce, 0x43, 0x3b, 0x59, 0x18, 0x00, 0x1b, 0xc0, 0x3b, 0xd4, 0x1e, 0xdf, 0x3b, 0x20, 
    0xf4, 0xf2, 0xb0, 0xf0, 0x2a, 0xb1, 0xe9, 0x9c, 0x90, 0x07, 0xe0, 0x05, 0xf0, 0x04, 0x88, 0x94, 
    0xf0, 0x0e, 0x80, 0x9d, 0x3b, 0x0f, 0x2d, 0x30, 0x09, 0xc0, 0x2b, 0xbb, 0xc3, 0x65, 0xb2, 0x8a, 
    0xaf, 0x6e, 0x3c, 0x32, 0x00, 0xd6, 0xef, 0xb2, 0x2e, 0xc4, 0x84, 0x9f, 0xaa, 0xa5, 0x22, 0xf1, 
    0xb8, 0x3a, 0x88, 0x77, 0x6c, 0xd3, 0x24, 0xd7, 0x65, 0x72, 0x28, 0x63, 0x3b, 0x41, 0x3c, 0xc5, 
    0x42, 0xc3, 0xa6, 0xa5, 0xc7, 0xf3, 0x91, 0x3c, 0xd1, 0x89, 0xa7, 0x21, 0xa1, 0x78, 0x79, 0x40, 
    0x57, 0xa1, 0xdd, 0xc6, 0x22, 0x92, 0xa4, 0x66, 0x41, 0xd8, 0x46, 0x8a, 0xc5, 0xa3, 0x07, 0x40, 
    0x8f, 0x4b, 0x46, 0xe9, 0x3e, 0x94, 0xa4, 0x66, 0x40, 0x9c, 0xc7, 0x36, 0x3a, 0x80, 0x69, 0x45, 
    0xe0, 0x01, 0xb0, 0x81, 0xdc, 0x2b, 0xe6, 0x74, 0x06, 0xee, 0x52, 0x25, 0xab, 0x32, 0xbd, 0xd6, 
    0xf4, 0x50, 0x31, 0xd7, 0x02, 0x48, 0xd7, 0x43, 0x23, 0x2b, 0x5c, 0xb2, 0x65, 0xa4, 0xd5, 0x54, 
    0x20, 0xb0, 0xc5, 0xed, 0xab, 0x45, 0x24, 0x54, 0xce, 0x67, 0x1a, 0x1a, 0xb7, 0xa8, 0x83, 0x50, 
    0xd1, 0x66, 0x46, 0x25, 0x28, 0xec, 0xa1, 0xad, 0x22, 0x99, 0xf8, 0x4d, 0x35, 0x34, 0xa0, 0x3e, 
    0xed, 0xdf, 0x4b, 0x27, 0x11, 0x1d, 0xe6, 0xb1, 0xee, 0xdb, 0x75, 0xcb, 0x51, 0x4f, 0xbc, 0x3b, 
    0x2c, 0x33, 0x74, 0xca, 0xf6, 0xc8, 0x24, 0x71, 0xe1, 0x88, 0xf9, 0xa6, 0xbe, 0x99, 0xdb, 0x0e, 
    0x70, 0xc3, 0x89, 0x28, 0x03, 0xc0, 0xa1, 0xe3, 0x63, 0x63, 0x15, 0xe5, 0xaf, 0x2c, 0xc3, 0xa6, 
    0xeb, 0x9a, 0x84, 0xdb, 0x60, 0xe9, 0x9a, 0xc1, 0x96, 0x02, 0xc1, 0x1a, 0x96, 0x43, 0xa3, 0x71, 
    0xc7, 0xc2, 0xf8, 0x39, 0x5c, 0xb9, 0x0e, 0x27, 0x73, 0xa6, 0xeb, 0x82, 0x3b, 0xd9, 0xc9, 0xf4, 
    0x91, 0xe1, 0x2e, 0x7b, 0xb7, 0x10, 0xd2, 0xfc, 0x26, 0x57, 0x18, 0x80, 0xb2, 0x04, 0xc6, 0xe3, 
    0xec, 0x62, 0x00, 0xc1, 0xdf, 0x6b, 0xfb, 0x64, 0xe3, 0xf5, 0xdf, 0xad, 0x3b, 0xf3, 0xc6, 0xeb, 
    0x43, 0x9f, 0xb7, 0x6e, 0x98, 0x02, 0xa0, 0xac, 0x01, 0x2b, 0xc0, 0x1a, 0xba, 0xca, 0xcc, 0xc4, 
    0xac, 0x5d, 0x3b, 0x4d, 0xd1, 0x53, 0x00, 0x8c, 0x35, 0x22, 0xef, 0xc5, 0x3c, 0x70, 0x69, 0xf7, 
    0x5b, 0x62, 0x01, 0x7b, 0x88, 0x9a, 0xf3, 0xc3, 0x35, 0x3c, 0xf7, 0xd0, 0x4d, 0xcd, 0xe5, 0x2d, 
    0xce, 0x8a, 0xf1, 0xfc, 0xb4, 0x05, 0x3a, 0x94, 0x78, 0x7d, 0xaf, 0x59, 0x8f, 0x38, 0xff, 0xd1, 
    0xbd, 0xcb, 0x2d, 0x70, 0x54, 0xa0, 0xec, 0xfe, 0x1f, 0x00, 0x59, 0x8d, 0xad, 0x5e, 0xff, 0x0f, 
    0xb5, 0xdd, 0x19, 0x4f, 0xd7, 0xda, 0x1c, 0x17, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 
    0xae, 0x42, 0x60, 0x82
};
const unsigned int popup_error_data_size = 884;

const unsigned char popup_info_data[] = {
    0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d, 0x49, 0x48, 0x44, 0x52, 
    0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x30, 0x08, 0x06, 0x00, 0x00, 0x00, 0x57, 0x02, 0xf9, 
    0x87, 0x00, 0x00, 0x00, 0x01, 0x73, 0x52, 0x47, 0x42, 0x00, 0xae, 0xce, 0x1c, 0xe9, 0x00, 0x00, 
    0x02, 0xe6, 0x49, 0x44, 0x41, 0x54, 0x68, 0x43, 0xed, 0x99, 0x01, 0x52, 0x1b, 0x31, 0x0c, 0x45, 
    0xad, 0x9b, 0xc0, 0x49, 0x0a, 0x27, 0x29, 0x9c, 0xa4, 0x70, 0x12, 0xe0, 0x24, 0xd0, 0x93, 0x90, 
    0x9b, 0xa8, 0xf9, 0x8c, 0x37, 0x23, 0xb4, 0x92, 0x2d, 0xd9, 0x6e, 0xd3, 0xcc, 0x64, 0x67, 0x98, 
    0x64, 0xc2, 0xae, 0xad, 0x27, 0x7d, 0xc9, 0xb6, 0x96, 0xca, 0x85, 0x5f, 0x74, 0xe1, 0xf6, 0x97, 
    0x2b, 0xc0, 0xb9, 0x23, 0xb8, 0x3c, 0x02, 0xcc, 0x7c, 0x53, 0xa1, 0xf0, 0xb9, 0x7d, 0x3f, 0xe0, 
    0x37, 0x22, 0xfa, 0x58, 0x0d, 0xbc, 0x04, 0xa0, 0x1a, 0xfd, 0xb3, 0x94, 0xf2, 0x20, 0x8c, 0xf6, 
    0x6c, 0x05, 0x0c, 0x40, 0xde, 0x56, 0x00, 0x4d, 0x01, 0x30, 0xf3, 0xaf, 0xa3, 0x95, 0x4f, 0x13, 
    0x5e, 0x05, 0xcc, 0x2b, 0x11, 0x3d, 0x8f, 0x8e, 0x31, 0x04, 0xc0, 0xcc, 0x77, 0xa5, 0x94, 0x97, 
    0x80, 0xb7, 0xa3, 0x76, 0x01, 0xe4, 0x99, 0x88, 0x5e, 0xa3, 0x0f, 0x6c, 0xf7, 0xa5, 0x01, 0x98, 
    0x19, 0x86, 0x43, 0x2a, 0xd6, 0xf5, 0xe5, 0xd1, 0x52, 0xca, 0xef, 0xe3, 0x3d, 0x07, 0x22, 0x3a, 
    0x18, 0x39, 0x01, 0xa9, 0xc1, 0x01, 0xd6, 0xf5, 0x94, 0x8d, 0x46, 0x18, 0xa0, 0x1a, 0x02, 0xe3, 
    0xf5, 0xe4, 0x69, 0x19, 0x74, 0x72, 0x06, 0xe3, 0xdd, 0x03, 0x3e, 0x12, 0x8d, 0x0c, 0xc0, 0xbb, 
    0x61, 0x7c, 0xda, 0x63, 0xd2, 0x28, 0x01, 0xa2, 0xf3, 0x28, 0x0c, 0x11, 0x02, 0x60, 0x66, 0x6d, 
    0x3c, 0x26, 0x78, 0x5c, 0x51, 0x45, 0x00, 0x54, 0x41, 0x30, 0xc7, 0x56, 0x76, 0xf1, 0xf3, 0x07, 
    0x11, 0xdd, 0xf7, 0xa2, 0xd0, 0x05, 0x30, 0x34, 0x0f, 0x6d, 0xdf, 0xf6, 0x06, 0xce, 0xfe, 0xdf, 
    0x81, 0x40, 0x85, 0x7a, 0x6c, 0x8d, 0xd5, 0x04, 0xa8, 0xd5, 0x06, 0x9e, 0xd9, 0xae, 0x70, 0x68, 
    0xb3, 0x00, 0x8d, 0x48, 0x20, 0x1f, 0xdc, 0x05, 0xb0, 0x07, 0xa0, 0xa5, 0xd3, 0x1c, 0xcc, 0xd0, 
    0xf7, 0xe9, 0xa7, 0x68, 0x52, 0xd6, 0x48, 0x7c, 0x4a, 0xa7, 0xb5, 0x22, 0xee, 0x02, 0x30, 0x33, 
    0x4a, 0x25, 0xaa, 0xce, 0x76, 0xa5, 0x12, 0x56, 0xe5, 0x4d, 0x48, 0xcf, 0xdb, 0x44, 0xc6, 0xdc, 
    0xc8, 0x37, 0x73, 0x8d, 0x68, 0x01, 0xc0, 0x0b, 0xa7, 0xa4, 0x3a, 0xd6, 0xe7, 0x6e, 0xbe, 0xa8, 
    0x08, 0xc8, 0xe8, 0x65, 0x01, 0x30, 0xaf, 0x4c, 0x6a, 0x37, 0xef, 0x4c, 0xa3, 0x32, 0x1e, 0xf0, 
    0xb4, 0x3e, 0x13, 0x81, 0x9a, 0x0f, 0x5a, 0x01, 0xa6, 0x7c, 0x3d, 0x80, 0x6f, 0xab, 0x6d, 0xd6, 
    0xfb, 0xd5, 0x00, 0xb9, 0xe0, 0x7d, 0xad, 0xca, 0x99, 0xc4, 0x36, 0x72, 0xc1, 0x94, 0xb0, 0x07, 
    0x20, 0xe5, 0xd3, 0x2d, 0x65, 0x19, 0xc3, 0x32, 0xf7, 0xaa, 0x28, 0x9a, 0x32, 0xf2, 0x00, 0x58, 
    0x4c, 0xe4, 0x26, 0x50, 0xcb, 0x98, 0x5a, 0x82, 0x65, 0x0e, 0xa5, 0x37, 0x6a, 0x86, 0x94, 0x6f, 
    0x75, 0x24, 0x77, 0x00, 0x46, 0xe8, 0xc2, 0xa5, 0x73, 0x55, 0x12, 0x8b, 0x6a, 0x04, 0x19, 0xca, 
    0x75, 0x68, 0x67, 0x8b, 0x05, 0xa0, 0x93, 0x67, 0x47, 0x1d, 0x91, 0xc1, 0x6c, 0x12, 0x8b, 0x85, 
    0x4d, 0xae, 0x09, 0x79, 0x80, 0x91, 0x04, 0xae, 0x93, 0x0f, 0x97, 0x51, 0x11, 0x01, 0x48, 0x50, 
    0x02, 0xec, 0xe4, 0xdc, 0x8d, 0xc0, 0x39, 0x01, 0xaa, 0x23, 0x9a, 0xf9, 0x68, 0x01, 0x68, 0xdd, 
    0x5d, 0x9c, 0x84, 0x74, 0xd8, 0xfe, 0xa7, 0x24, 0x1e, 0xaa, 0x42, 0xa9, 0x3d, 0x90, 0xd0, 0xef, 
    0x8a, 0x1c, 0xe8, 0x16, 0x14, 0x6f, 0x1d, 0x58, 0x31, 0xf9, 0xea, 0x31, 0xd0, 0x57, 0xda, 0xd9, 
    0xeb, 0x01, 0xc8, 0x76, 0xc9, 0xd0, 0x01, 0x66, 0x51, 0x19, 0x95, 0x09, 0x6c, 0xee, 0x08, 0x3c, 
    0x00, 0x9d, 0xc8, 0xe9, 0xd5, 0x78, 0x16, 0xc0, 0x58, 0x85, 0xe3, 0x9b, 0xb9, 0x5a, 0xbe, 0xe4, 
    0x7e, 0x28, 0x1d, 0x85, 0x05, 0x00, 0xa1, 0xed, 0x7c, 0xe6, 0x40, 0x93, 0xda, 0xd4, 0xcd, 0x00, 
    0x18, 0x1d, 0xbf, 0xa1, 0x03, 0x0d, 0xca, 0xa9, 0xec, 0x03, 0xa5, 0x3a, 0x11, 0xa3, 0x00, 0xd6, 
    0x39, 0x7c, 0xe8, 0x48, 0x29, 0xf6, 0xf4, 0x43, 0x87, 0xfa, 0x6a, 0xc8, 0xa9, 0x19, 0x10, 0x39, 
    0x0f, 0x18, 0x1b, 0x49, 0x3c, 0x3f, 0x7e, 0xa8, 0xaf, 0x10, 0xba, 0x81, 0xfb, 0x57, 0x3a, 0x13, 
    0x4e, 0xe7, 0xaf, 0x2b, 0xdb, 0xee, 0x39, 0xd7, 0x19, 0x78, 0x29, 0x84, 0x21, 0x1b, 0xf8, 0x2e, 
    0x74, 0x8e, 0xee, 0x02, 0x34, 0xfa, 0x35, 0xe9, 0x9e, 0xa8, 0xb5, 0x0d, 0x77, 0x5a, 0xf4, 0xe1, 
    0xaa, 0x17, 0x02, 0x10, 0x10, 0x90, 0x93, 0xee, 0x4c, 0xa7, 0x41, 0x1a, 0x3d, 0xd1, 0xb0, 0xe7, 
    0x37, 0x67, 0x84, 0x01, 0x04, 0x04, 0xda, 0xe3, 0xd6, 0x4b, 0x0d, 0x80, 0xe0, 0xef, 0xad, 0x7e, 
    0x5a, 0xed, 0xf5, 0x1f, 0xb5, 0x41, 0xec, 0xb5, 0xd7, 0xbb, 0x9a, 0xd7, 0x51, 0x4c, 0x01, 0xa8, 
    0xc6, 0x13, 0xa2, 0x21, 0x9b, 0xb1, 0x91, 0x83, 0x9a, 0x77, 0x4f, 0xaa, 0x44, 0xcb, 0x41, 0x86, 
    0x00, 0x54, 0x34, 0x22, 0xef, 0xc5, 0x5a, 0x86, 0xff, 0xfb, 0x57, 0x4c, 0xda, 0x1a, 0xa1, 0x69, 
    0x48, 0xc3, 0x93, 0xc7, 0x69, 0x4d, 0xd8, 0xde, 0xe2, 0xac, 0x68, 0xcf, 0x0f, 0x47, 0xc0, 0x73, 
    0xe9, 0x45, 0xbe, 0x66, 0x9d, 0x11, 0xff, 0xec, 0xb3, 0xcb, 0x23, 0x30, 0x6b, 0x50, 0xf6, 0xf9, 
    0x2b, 0x40, 0xd6, 0x63, 0xab, 0xef, 0xff, 0x03, 0xf9, 0xd1, 0xde, 0x40, 0x4d, 0xab, 0xc6, 0x23, 
    0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82
};
const unsigned int popup_info_data_size = 812;

const unsigned char popup_warning_data[] = {
    0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d, 0x49, 0x48, 0x44, 0x52, 
    0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x30, 0x08, 0x06, 0x00, 0x00, 0x00, 0x57, 0x02, 0xf9, 
    0x87, 0x00, 0x00, 0x00, 0x01, 0x73, 0x52, 0x47, 0x42, 0x00, 0xae, 0xce, 0x1c, 0xe9, 0x00, 0x00, 
    0x02, 0x8f, 0x49, 0x44, 0x41, 0x54, 0x68, 0x43, 0xed, 0x99, 0xed, 0x71, 0xc2, 0x30, 0x0c, 0x86, 
    0xad, 0x4d, 0xca, 0x24, 0x2d, 0x9b, 0x94, 0x49, 0x4a, 0x27, 0x29, 0x9d, 0x84, 0x76, 0x92, 0xd2, 
    0x49, 0xd4, 0x88, 0xb3, 0x39, 0xc5, 0x91, 0x6c, 0xd9, 0x56, 0xe8, 0x71, 0x97, 0xfc, 0xe1, 0x0e, 
    0xec, 0xd8, 0x8f, 0xbe, 0x5e, 0xd9, 0x40, 0x78, 0xf0, 0x07, 0x1e, 0x7c, 0xff, 0x61, 0x03, 0xf8, 
    0x6f, 0x0f, 0x6e, 0x1e, 0x28, 0x79, 0x00, 0x11, 0x5f, 0xe8, 0x77, 0x00, 0xf8, 0x5a, 0xcb, 0x53, 
    0xab, 0x78, 0x00, 0x11, 0x9f, 0x42, 0x08, 0x1f, 0x21, 0x84, 0x2b, 0xc0, 0xf4, 0x49, 0x00, 0x07, 
    0x00, 0xb8, 0x78, 0x83, 0xac, 0x05, 0xf0, 0x13, 0x42, 0x20, 0x08, 0xfe, 0xd0, 0xe6, 0xf7, 0xde, 
    0x10, 0xee, 0x00, 0x88, 0xf8, 0x36, 0xed, 0xfa, 0xa8, 0x58, 0xfa, 0x08, 0x00, 0xef, 0x9e, 0x5e, 
    0x70, 0x05, 0x88, 0xa1, 0x43, 0xd6, 0x4f, 0x0f, 0x85, 0x0e, 0x59, 0xfe, 0x95, 0x7d, 0xb7, 0xf3, 
    0xf4, 0x82, 0x37, 0x40, 0x6e, 0xfd, 0x5d, 0xdc, 0xf8, 0x99, 0x85, 0x94, 0xab, 0x17, 0xdc, 0x00, 
    0x04, 0xeb, 0xdf, 0x36, 0x2a, 0x84, 0x95, 0x9b, 0x17, 0x3c, 0x01, 0xc8, 0xca, 0xa9, 0xea, 0x5c, 
    0x00, 0x20, 0x59, 0x3f, 0x44, 0x38, 0xee, 0x85, 0x13, 0x00, 0x1c, 0x3c, 0x72, 0xc1, 0x05, 0x20, 
    0xd6, 0x7b, 0xda, 0x60, 0x7a, 0x16, 0x61, 0xb2, 0x96, 0x17, 0xbc, 0x00, 0x78, 0xd9, 0x9c, 0x59, 
    0x9f, 0x5b, 0x19, 0x11, 0xf9, 0x38, 0x17, 0x6d, 0x18, 0x06, 0x10, 0x2c, 0x4b, 0x82, 0x75, 0x92, 
    0xc2, 0xa3, 0x65, 0xac, 0x35, 0xbc, 0x3c, 0x00, 0x90, 0x2d, 0xa6, 0x5a, 0x3f, 0x8d, 0xc9, 0xbc, 
    0x30, 0x2c, 0x6e, 0x43, 0x00, 0x82, 0x45, 0x49, 0x69, 0xaf, 0x7d, 0x4f, 0xcc, 0x8b, 0xe7, 0xb8, 
    0xf1, 0xdf, 0xe4, 0x15, 0x4b, 0xbe, 0x58, 0xad, 0x4f, 0xe3, 0xba, 0x01, 0x24, 0xd1, 0x02, 0x80, 
    0x3d, 0xb3, 0x34, 0xf5, 0x42, 0x49, 0xc0, 0x66, 0x55, 0x07, 0x11, 0xf9, 0x6f, 0x34, 0xa5, 0xbb, 
    0xac, 0x8e, 0x00, 0x2c, 0x44, 0x8b, 0x2b, 0x6c, 0xb6, 0xc9, 0x1c, 0x80, 0xfa, 0x24, 0x17, 0x71, 
    0xeb, 0x02, 0x28, 0x89, 0x96, 0xc5, 0x03, 0x31, 0xc4, 0x8a, 0x06, 0xb0, 0x86, 0x51, 0x2f, 0x80, 
    0x2a, 0x5a, 0x56, 0x80, 0x08, 0x31, 0x2b, 0xab, 0x3c, 0x04, 0x57, 0x03, 0xb0, 0x26, 0x61, 0x29, 
    0x84, 0x18, 0x64, 0xee, 0x05, 0xb5, 0x04, 0x6b, 0x40, 0xcd, 0x1e, 0xc8, 0xcb, 0x20, 0x6f, 0x19, 
    0x32, 0xd1, 0x52, 0x93, 0xb8, 0x20, 0x6e, 0xcd, 0x65, 0xb5, 0x09, 0xa0, 0x45, 0x88, 0x2c, 0x1e, 
    0x88, 0x61, 0x44, 0x95, 0x8a, 0x60, 0xd5, 0x36, 0xa4, 0x14, 0x4e, 0x66, 0x00, 0x21, 0x71, 0x8b, 
    0xa2, 0x65, 0x05, 0x10, 0x72, 0xa1, 0xc9, 0x0b, 0x2d, 0x00, 0x79, 0xbc, 0xde, 0x44, 0x4b, 0x69, 
    0x1b, 0x4c, 0x21, 0xc4, 0x44, 0xaf, 0xd8, 0x0c, 0x0e, 0xe5, 0x80, 0x60, 0xfd, 0x6a, 0x3b, 0xdc, 
    0xe2, 0x81, 0x08, 0xd1, 0x25, 0x6e, 0x26, 0x0f, 0xe4, 0xb1, 0x3f, 0x9d, 0x6b, 0xab, 0xf3, 0x3a, 
    0x00, 0xba, 0xc4, 0xcd, 0xb2, 0x11, 0x7a, 0x31, 0x3f, 0xe7, 0x9a, 0x8e, 0x84, 0xe9, 0x4e, 0x28, 
    0xba, 0x9e, 0xf2, 0xa5, 0x7a, 0xa5, 0xd2, 0x73, 0x66, 0xb0, 0x00, 0x54, 0x45, 0xcb, 0x2a, 0x3a, 
    0x96, 0x71, 0xf9, 0x99, 0xa1, 0x26, 0x6e, 0x45, 0x00, 0xab, 0x68, 0x29, 0x49, 0x4c, 0xe5, 0x31, 
    0x75, 0xa3, 0xdf, 0xda, 0x19, 0x21, 0x9f, 0xdb, 0x52, 0xaa, 0xab, 0xdd, 0xa8, 0x55, 0xb4, 0x46, 
    0xab, 0x90, 0x00, 0x31, 0x3b, 0xe1, 0x95, 0x2e, 0xc4, 0x54, 0x0f, 0x94, 0x7a, 0x7d, 0x63, 0x28, 
    0x98, 0xcb, 0xa8, 0x00, 0x60, 0x16, 0x37, 0x11, 0xa0, 0xd6, 0xeb, 0x1b, 0x01, 0xba, 0x42, 0x88, 
    0xf5, 0x49, 0xf9, 0xf5, 0xa4, 0x78, 0x66, 0xd0, 0x00, 0x5c, 0x5a, 0x5d, 0x0b, 0xa8, 0x36, 0x26, 
    0xe6, 0x1f, 0x79, 0x31, 0xdd, 0xb1, 0x8a, 0xd5, 0x6f, 0x01, 0x60, 0xe9, 0xf5, 0x47, 0x36, 0xd6, 
    0x32, 0xd7, 0x72, 0x72, 0x93, 0x00, 0xe8, 0x72, 0x8a, 0xcb, 0xfa, 0xed, 0x82, 0xaa, 0x65, 0x71, 
    0xa7, 0xb1, 0x49, 0xdc, 0xd2, 0xeb, 0x16, 0xed, 0xb6, 0x04, 0x90, 0x27, 0x90, 0xd3, 0x5e, 0x5c, 
    0x5e, 0x53, 0x07, 0x10, 0xba, 0x43, 0x97, 0x95, 0x1d, 0x5e, 0x22, 0x76, 0xbf, 0x5a, 0x12, 0x53, 
    0x18, 0xf1, 0x04, 0x72, 0x58, 0x7f, 0xe8, 0x15, 0x74, 0x51, 0xf6, 0x29, 0xfd, 0x55, 0x55, 0x53, 
    0xe2, 0xfc, 0x5f, 0x96, 0xa1, 0x5d, 0xf4, 0x4e, 0x2e, 0xf5, 0x51, 0xd5, 0x5e, 0xa8, 0x77, 0xd1, 
    0x7b, 0xcd, 0xdb, 0x00, 0xee, 0x65, 0x69, 0x6d, 0x9d, 0xcd, 0x03, 0x9b, 0x07, 0x06, 0x2d, 0xf0, 
    0x07, 0xa1, 0xd6, 0x99, 0x40, 0xab, 0x52, 0x75, 0x97, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 
    0x44, 0xae, 0x42, 0x60, 0x82
};
const unsigned int popup_warning_data_size = 725;

#endif // POPUP_RESOURCES_H
