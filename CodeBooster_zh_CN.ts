<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="zh_CN">
<context>
    <name>QtC::CodeGeeX2</name>
    <message>
        <location filename="codegeex2hoverhandler.cpp" line="37"/>
        <source>Select Previous CodeGeeX2 Suggestion</source>
        <translation>选择上一条CodeGeeX2建议</translation>
    </message>
    <message>
        <location filename="codegeex2hoverhandler.cpp" line="41"/>
        <source>Select Next CodeGeeX2 Suggestion</source>
        <translation>选择下一条CodeGeeX2建议</translation>
    </message>
    <message>
        <location filename="codegeex2hoverhandler.cpp" line="44"/>
        <source>Apply (%1)</source>
        <translation>全部应用(%1)</translation>
    </message>
    <message>
        <location filename="codegeex2hoverhandler.cpp" line="45"/>
        <source>Apply Line</source>
        <translation>应用一行</translation>
    </message>
    <message>
        <location filename="codegeex2hoverhandler.cpp" line="47"/>
        <source>Apply Word (%1)</source>
        <translation>应用一个单词(%1)</translation>
    </message>
    <message>
        <location filename="codegeex2hoverhandler.cpp" line="61"/>
        <source>%1 of %2</source>
        <translation>第%1条，共%2条</translation>
    </message>
    <message>
        <location filename="codegeex2plugin.cpp" line="68"/>
        <source>Request CodeGeeX2 Suggestion</source>
        <translation>请求CodeGeeX2建议</translation>
    </message>
    <message>
        <location filename="codegeex2plugin.cpp" line="70"/>
        <source>Request CodeGeeX2 suggestion at the current editor&apos;s cursor position.</source>
        <translation>在当前编辑器光标位置请求CodeGeeX2建议。</translation>
    </message>
    <message>
        <location filename="codegeex2plugin.cpp" line="82"/>
        <source>Show next CodeGeeX2 Suggestion</source>
        <translation>显示下一条CodeGeeX2建议</translation>
    </message>
    <message>
        <location filename="codegeex2plugin.cpp" line="83"/>
        <source>Cycles through the received CodeGeeX2 Suggestions showing the next available Suggestion.</source>
        <translation>在收到的CodeGeeX2建议中循环，显示上一条可用的建议。</translation>
    </message>
    <message>
        <location filename="codegeex2plugin.cpp" line="94"/>
        <source>Show previos CodeGeeX2 Suggestion</source>
        <translation>显示上一条CodeGeeX2建议</translation>
    </message>
    <message>
        <location filename="codegeex2plugin.cpp" line="95"/>
        <source>Cycles through the received CodeGeeX2 Suggestions showing the previous available Suggestion.</source>
        <translation>在收到的CodeGeeX2建议中循环，显示下一条可用的建议。</translation>
    </message>
    <message>
        <location filename="codegeex2plugin.cpp" line="106"/>
        <source>Disable CodeGeeX2</source>
        <translation>禁用CodeGeeX2</translation>
    </message>
    <message>
        <location filename="codegeex2plugin.cpp" line="107"/>
        <location filename="codegeex2plugin.cpp" line="137"/>
        <source>Disable CodeGeeX2.</source>
        <translation>禁用CodeGeeX2。</translation>
    </message>
    <message>
        <location filename="codegeex2plugin.cpp" line="115"/>
        <location filename="codegeex2settings.cpp" line="12"/>
        <location filename="codegeex2settings.cpp" line="13"/>
        <source>Enable CodeGeeX2</source>
        <translation>启用CodeGeeX2</translation>
    </message>
    <message>
        <location filename="codegeex2plugin.cpp" line="116"/>
        <location filename="codegeex2plugin.cpp" line="137"/>
        <source>Enable CodeGeeX2.</source>
        <translation>启用CodeGeeX2。</translation>
    </message>
    <message>
        <location filename="codegeex2plugin.cpp" line="124"/>
        <source>Toggle CodeGeeX2</source>
        <translation>切换CodeGeeX2启用/禁用</translation>
    </message>
    <message>
        <location filename="codegeex2plugin.cpp" line="155"/>
        <source>CodeGeeX2</source>
        <translation>CodeGeeX2</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="14"/>
        <source>Enables the CodeGeeX2 integration.</source>
        <translation>启用CodeGeeX2集成。</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="24"/>
        <source>Auto Complete</source>
        <translation>自动补全</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="26"/>
        <source>Request completions automatically</source>
        <translation>自动请求补全</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="29"/>
        <source>Automatically request suggestions for the current text cursor position after changes to the document.</source>
        <translation>文档修改后自动在当前光标位置请求建议。</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="35"/>
        <source>URL of CodeGeeX2 API:</source>
        <translation>CodeGeeX2 AP的URL：</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="37"/>
        <source>CodeGeeX2 API URL</source>
        <translation>CodeGeeX2 AP的URL</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="39"/>
        <source>Input URL of CodeGeeX2 API.</source>
        <translation>输入CodeGeeX2 API的URL。</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="44"/>
        <source>Context length limit:</source>
        <translation>上下文长度限制：</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="45"/>
        <source>Context length limit</source>
        <translation>上下文长度限制</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="47"/>
        <source>Maximum length of context send to server.</source>
        <translation>发送给服务器的最大上下文长度限制。</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="52"/>
        <source>Output sequence length:</source>
        <translation>输出序列长度：</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="53"/>
        <source>Output sequence length</source>
        <translation>输出序列长度</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="55"/>
        <source>Number of tokens to generate each time.</source>
        <translation>每次生成的token数量。</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="60"/>
        <source>Temperature:</source>
        <translation>Temperature：</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="61"/>
        <source>Temperature</source>
        <translation>Temperature</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="63"/>
        <source>Affects how &quot;random&quot; the model’s output is.</source>
        <translation>影响模型输出的随机性。</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="68"/>
        <source>Top K:</source>
        <translation>Top K：</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="69"/>
        <source>Top K</source>
        <translation>Top K</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="71"/>
        <location filename="codegeex2settings.cpp" line="79"/>
        <source>Affects how &quot;random&quot; the model&apos;s output is.</source>
        <translation>影响模型输出的随机性。</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="76"/>
        <source>Top P:</source>
        <translation>Top P：</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="77"/>
        <source>Top P</source>
        <translation>Top P</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="84"/>
        <source>Seed:</source>
        <translation>随机数种子：</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="85"/>
        <source>Seed</source>
        <translation>随机数种子</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="87"/>
        <source>Random number seed.</source>
        <translation>随机数种子。</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="89"/>
        <location filename="codegeex2settings.cpp" line="91"/>
        <source>Try to expand headers (experimnetal)</source>
        <translation>尝试展开头文件（试验）</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="94"/>
        <source>Try to expand headers when sending requests.</source>
        <translation>在发送请求时，尝试展开头文件。</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="96"/>
        <location filename="codegeex2settings.cpp" line="98"/>
        <source>Brace balance (experimnetal)</source>
        <translation>大括号平衡（试验）</translation>
    </message>
    <message>
        <location filename="codegeex2settings.cpp" line="101"/>
        <source>Stop suggestions from breaking brace balance.</source>
        <translation>防止建议破坏大括号平衡。</translation>
    </message>
</context>
</TS>
