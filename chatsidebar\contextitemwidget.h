#ifndef CONTEXTITEMWIDGET_H
#define CONTEXTITEMWIDGET_H

#include <QWidget>
#include <QToolBar>
#include <QAction>
#include <QFrame>
#include <QLabel>

#include "chatcontext/contextitem.h"

namespace Ui {
class ContextItemWidget;
}

namespace CodeBooster::Internal {

class ButtonLabel;

class ContextItemWidget : public QFrame
{
    Q_OBJECT

public:
    explicit ContextItemWidget(const ContextItem &context, QWidget *parent = nullptr);
    ~ContextItemWidget();

    ContextItem context() const;
    void setShowDiscardBtn(bool show);

protected:
    void enterEvent(QEnterEvent* event) override;
    void leaveEvent(QEvent* event) override;
    void mouseReleaseEvent(QMouseEvent* event) override;

private:
    void updateStyleSheet(bool hover);

signals:
    void clicked(QString uri);
    void discard();

private:
    Ui::ContextItemWidget *ui;

    ContextItem mContext;
    ButtonLabel *mDiscardButton;

    QString mStyleSheet;
    QString mHoverStyleSheet;

    bool mClickTriggerByDiscard = false;
};

class ContextItemContainer : public QFrame
{
    Q_OBJECT
public:
    explicit ContextItemContainer(bool showDiscardButton = true, QWidget *parent = nullptr);
    ~ContextItemContainer();

    void newContextItemWgt(const ContextItem &context);
    QList<ContextItem> allContext() const;
    void clearContext();
    void updateActionState();
    void showContext();

private slots:
    void onActionExpandTriggered();
    void onDiscardContext();

private:
    void updateInfoLabel();
    QList<ContextItemWidget*> contextWidgets() const;

signals:
    void contextRemoved(const ContextItem &context);

private:
    bool mShowDicardBtn;

    QToolBar *mToolBar;
    QAction *mExpandAction;
    QLabel *mInfoLabel;

    QWidget *mContextArea;
};

/**
 * @brief The ButtonLabel class 可点击，具备交互效果的Label
 */
class ButtonLabel : public QLabel {
    Q_OBJECT

public:
    explicit ButtonLabel(QWidget* parent = nullptr);
    ~ButtonLabel();

    void setIcon(const QIcon &icon, QSize size = QSize(16, 16));
    void setText(const QString &text);

signals:
    void clicked(QString text);  // 信号：鼠标左键点击时发送

protected:
    void enterEvent(QEnterEvent* event) override;  // 鼠标进入事件
    void leaveEvent(QEvent* event) override;  // 鼠标离开事件
    void mouseReleaseEvent(QMouseEvent* event) override;  // 鼠标按下事件

private:
    void setHoverStyle(bool hover);  // 设置悬浮样式

private:
    QString mStyleSheet;
    QString mHoverStyleSheet;
};

}
#endif // CONTEXTITEMWIDGET_H
