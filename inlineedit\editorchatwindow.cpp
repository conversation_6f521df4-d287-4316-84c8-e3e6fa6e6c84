#include "editorchatwindow.h"
#include "ui_editorchatwindow.h"

#include <QTimer>
#include <QJsonArray>
#include <QKeyEvent>
#include <QScrollBar>
#include <QCommonStyle>
#include <QTextCursor>

#include <utils/utilsicons.h>
#include <texteditor/textdocumentlayout.h>

#include "common/widgettheme.h"
#include "codeboosterplugin.h"
#include "pluginsettings/codeboostersettings.h"
#include "chatcontext/contextitem.h"
#include "database/chatdatabase.h"
#include "common/llmiconfactory.h"
#include "common/useroptions.h"

using namespace TextEditor;

namespace CodeBooster::Internal{

EditorChatWindow::EditorChatWindow(QStringList history, QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::EditorChatWindow)
    , mToolBar(nullptr)
    , mActionClose(nullptr)
    , mFirstShow(true)
    , mFirstActivate(true)
    , mPosition(0)
    , mLastVisibleSliderPos(0)
    , mRequestTimer(new QTimer(this))
{
    ui->setupUi(this);

    setObjectName("EditorChatWindow");
    setAttribute(Qt::WA_DeleteOnClose);

    // 不继承编辑器字体
    QFont appFont = QApplication::font();
    appFont.setPointSize(appFont.pointSize() - 1);
    setFont(appFont);

    setWindowFlags(Qt::ToolTip);
    setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint);

    setMaximumSize(380, 68);
    setMinimumSize(380, 68);

    mTextEditor = BaseTextEditor::currentTextEditor()->editorWidget();
    mEditorCursor = mTextEditor->textCursor();
    mEditorHilightLine = mTextEditor->highlightCurrentLine();
    mLastVisibleSliderPos = mTextEditor->verticalScrollBar()->value();

    setParent(mTextEditor);
    setStyleSheet(CB_THEME.SS_EditorChatWindow);
    setAttribute(Qt::WA_StyledBackground);

    // 初始化顶部工具栏
    {
        mActionClose = new QAction(Utils::Icons::CLOSE_TOOLBAR.icon(), "关闭", this);
        connect(mActionClose, &QAction::triggered, this, &EditorChatWindow::closeWindow);

        mToolBar = new QToolBar(this);
        mToolBar->setIconSize({12, 12});
        mToolBar->setProperty("_q_custom_style_disabled", QVariant(true));// 使得toolBar高度可以跟随Action的图标高度

        ui->horizontalLayout_action->addWidget(mToolBar);
        mToolBar->addAction(mActionClose);
    }

    updatePosition();
    activateInput();

    // 初始化输入框
    {
        connect(ui->lineEdit_instruction, &QLineEdit::returnPressed, this, &EditorChatWindow::inputReturnPressed);
        connect(ui->lineEdit_instruction, &QLineEdit::textChanged, this, &EditorChatWindow::instTextChanged);
        ui->lineEdit_instruction->setHistory(history);
        ui->lineEdit_instruction->setPlaceholderText("Enter键生成，↑↓键浏览历史指令");

        mSpinner = new SpinnerSolution::Spinner(SpinnerSolution::SpinnerSize::Small, ui->lineEdit_instruction);
        mSpinner->setVisible(false);

        ui->label_info->setText(QString());
        ui->label_info->setVisible(false);
        ui->label_info->setStyleSheet("color:red");
    }

    // 初始化建议功能按钮
    {
        setSuggestionBtnState(false);

        connect(ui->pushButton_accept, &QPushButton::clicked, this, [=](){
            setAcceptSuggestion(true);
        });

        connect(ui->pushButton_reject, &QPushButton::clicked, this, [=](){
            setAcceptSuggestion(false);
        });

        ui->pushButton_cancel->setVisible(false);
        connect(ui->pushButton_cancel, &QPushButton::clicked, this, [=](){
            cancelRunningRequest();
        });
    }

    // 初始化指令选择框
    {
        ui->comboBox_inst->setFixedWidth(70);
        ui->comboBox_inst->addItem("选择指令");
        QStringList insts = CodeBoosterSettings::instance().codeInstructions();
        ui->comboBox_inst->addItems(insts);
        connect(ui->comboBox_inst, static_cast<void (QComboBox::*)(int)>(&QComboBox::currentIndexChanged), this, &EditorChatWindow::instSeleted);
    }

    // 加载模型信息
    {
        for (const auto &param : CodeBoosterSettings::instance().chatParams())
        {
            ui->comboBox_model->addItem(llmIcon(param.title), param.title, QVariant::fromValue(param));
        }

        connect(ui->comboBox_model, &QComboBox::currentTextChanged, this, [this](QString text) {
            emit modelChanged(text);
            ui->comboBox_model->setToolTip(ui->comboBox_model->currentText());
        });
        ui->comboBox_model->setToolTip(ui->comboBox_model->currentText());
        ui->comboBox_model->setFixedWidth(100);
    }


    // TODO: 按行接受请求
    // TODO: 是否模仿cursor添加快速对话功能？
}

EditorChatWindow::~EditorChatWindow()
{
    delete ui;
}

void EditorChatWindow::activateInput()
{
    ui->lineEdit_instruction->setFocus();
    if (!mFirstActivate)
    {
        mEditorCursor = mTextEditor->textCursor(); // 每次进入补全对话框时都更新编辑器光标位置
        mFirstActivate = false;
    }

    scrollEditorToShowWindow();

    hilightEditorLine(true);
    mTextEditor->setTextCursor(mCompletionPosCursor);
}

TextEditorWidget *EditorChatWindow::textEditor() const
{
    return mTextEditor;
}

void EditorChatWindow::updatePosition()
{
    TextEditorWidget *editorWidget = textEditor();
    QTextCursor cursor = editorWidget->textCursor();

    if (mFirstShow)
    {
        cursor.movePosition(QTextCursor::StartOfLine);
        mFirstShow = false;
        mPosition = cursor.position();

        // 第一次显示时记录补全位置光标
        {
            QTextCursor textCursor = mTextEditor->textCursor();
            EditorChat::adjustCursor(textCursor);

            mCompletionPosCursor = textCursor;

            // 更新文本编辑器的光标
            mTextEditor->setTextCursor(mCompletionPosCursor);
        }
    }
    else
    {
        cursor.setPosition(mPosition);
    }

    const int line = cursor.blockNumber();

    QPoint m_offset = QPoint(0, -height() - 4);

    const QPoint screenPos = editorWidget->toolTipPosition(cursor) + m_offset;
    const QRect toolTipArea = QRect(screenPos, QSize(sizeHint()));
    const QRect plainTextArea = QRect(editorWidget->mapToGlobal(QPoint(0, 0)), editorWidget->size());
    const bool visible = plainTextArea.intersects(toolTipArea);

    if (visible)
    {
        if (auto p = parentWidget())
        {
            move(p->mapFromGlobal(screenPos));
        }
        else
        {
            move(screenPos);
        }

        show();
    }
    else
    {
        hide();
    }
}

void EditorChatWindow::replyStateChanged(bool running)
{
    // 设置控件状态
    ui->lineEdit_instruction->setDisabled(running);
    setSuggestionBtnState(!running);
    ui->pushButton_cancel->setVisible(running);

    mSpinner->setVisible(running);

    if (running)
    {
        ui->label_info->setVisible(false);

        // 启动超时定时器
        mRequestTimer->setSingleShot(true);
        connect(mRequestTimer, &QTimer::timeout, this, &EditorChatWindow::requestTimeout);
        mRequestTimer->start(30000);
    }
    else
    {
        mRequestTimer->stop();

        // 收到回复后将输入框设置为焦点，方便输入快捷键接受或拒绝建议
        // 如果立即设置为焦点编辑器会夺取焦点
        QTimer::singleShot(50, [this]() {
            activateWindow();
            ui->lineEdit_instruction->setFocus();
        });
    }
}

void EditorChatWindow::setCurrentModel(const QString &modelName)
{
    if (modelName.isEmpty())
        return;

    ui->comboBox_model->blockSignals(true);
    ui->comboBox_model->setCurrentText(modelName);
    ui->comboBox_model->setToolTip(ui->comboBox_model->currentText());
    ui->comboBox_model->blockSignals(false);
}

void EditorChatWindow::scrollEditorToShowWindow()
{
    if (!isVisible())
    {
        mTextEditor->verticalScrollBar()->setValue(mLastVisibleSliderPos);
    }
}

bool EditorChatWindow::event(QEvent *e)
{
    if (e->type() == QEvent::ShortcutOverride)
    {
        QKeyEvent *ke = static_cast<QKeyEvent *>(e);
        if (ke->modifiers().testFlag(Qt::ControlModifier))
        {
            if (ke->key() == Qt::Key_Backspace)
            {
                // 请求执行时取消当前请求
                if (requestIsRunning())
                {
                    cancelRunningRequest();
                }
                // 取消建议
                else
                {
                    if (TextSuggestion *sug = mTextEditor->currentSuggestion())
                    {
                        setAcceptSuggestion(false);

                        // 防止QLineEdit接受Ctrl+Back快捷键删除行内容
                        ui->lineEdit_instruction->setEnabled(false);
                        QTimer::singleShot(0, [this]() {
                            ui->lineEdit_instruction->setEnabled(true);
                            ui->lineEdit_instruction->setFocus();
                        });
                    }
                }

                e->accept();
                return true;
            }
            else if ((ke->key() == Qt::Key_Enter) || (ke->key() == Qt::Key_Return))
            {
                setAcceptSuggestion(true);

                e->accept();
                return true;
            }
        }
        else
        {
            if (ke->key() == Qt::Key_Escape)
            {
                if (ui->lineEdit_instruction->text().isEmpty())
                {
                    focusTextEditor();
                    closeWindow();
                    return true;
                }
                else
                {
                    focusTextEditor();
                    return true;
                }
            }
            else if (ke->key() == Qt::Key_K)
            {
                focusTextEditor();
                return true;
            }
            else if ((ke->key() == Qt::Key_Enter) || (ke->key() == Qt::Key_Return))
            {
                e->accept();
                return true;
            }
        }
    }
    else if (e->type() == QEvent::FocusIn)
    {
        hilightEditorLine(true);
    }
    else if (e->type() == QEvent::FocusOut)
    {
        hilightEditorLine(false);
    }

    return QWidget::event(e);
}

void EditorChatWindow::keyPressEvent(QKeyEvent *e)
{
    // 保留此函数，防止按下Enter时触发编辑器换行
}

void EditorChatWindow::inputReturnPressed()
{
    QString text = ui->lineEdit_instruction->text();
    if (text.isEmpty())
        return;

    scrollEditorToShowWindow();

    CodeBooster::ModelParam modelParam = currentModelParam();
    if (modelParam.modelName.isEmpty())
        return;

    mTextEditor->clearSuggestion();
    emit sendInstructionText(text, modelParam);
}

void EditorChatWindow::instTextChanged(QString text)
{
    bool textExist = text.isEmpty();
    ui->label_close->setVisible(textExist);
}

void EditorChatWindow::instSeleted(int index)
{
    if (index == 0)
        return;

    QString instText = ui->comboBox_inst->currentText();
    ui->comboBox_inst->blockSignals(true);
    ui->comboBox_inst->setCurrentIndex(0);
    ui->comboBox_inst->blockSignals(false);

    if (!instText.isEmpty())
    {
        ui->lineEdit_instruction->setText(instText);
        ui->lineEdit_instruction->remeberCurrentText();
        inputReturnPressed();
    }
}

void EditorChatWindow::closeWindow()
{
    setAcceptSuggestion(false);
    hilightEditorLine(false);
    close();
}

void EditorChatWindow::requestTimeout()
{
    cancelRunningRequest();

    ui->label_info->show();
    ui->label_info->setText("请求超时");
}

void EditorChatWindow::focusTextEditor()
{
    // 暂时屏蔽当前窗口的事件
    this->setEnabled(false);

    // 使用 QTimer 延迟焦点设置
    QTimer::singleShot(0, [this]() {
        // 确保 mTextEditor 获得焦点
        mTextEditor->activateWindow();
        mTextEditor->setFocus();
        mTextEditor->setTextCursor(mEditorCursor);
        hilightEditorLine(false);

        // 恢复当前窗口的事件
        this->setEnabled(true);
    });
}

ModelParam EditorChatWindow::currentModelParam() const
{
    if (ui->comboBox_model->count() == 0)
        return ModelParam();

    ModelParam param = ui->comboBox_model->currentData().value<ModelParam>();
    return param;
}

void EditorChatWindow::hilightEditorLine(bool hl)
{
    if (hl)
    {
        mTextEditor->setHighlightCurrentLine(true);
    }
    else
    {
        mTextEditor->setHighlightCurrentLine(mEditorHilightLine);
    }
}

void EditorChatWindow::setSuggestionBtnState(bool visible)
{
    ui->pushButton_accept->setVisible(visible);
    ui->pushButton_reject->setVisible(visible);
}

void EditorChatWindow::setAcceptSuggestion(bool accept)
{
    if (accept)
    {
        if (TextSuggestion *sug = mTextEditor->currentSuggestion())
        {
            // 防止按下组合快捷键后又触发发送指令功能
            ui->lineEdit_instruction->setEnabled(false);

            sug->apply();
            closeWindow();
        }
    }
    else
    {
        mTextEditor->clearSuggestion();
        setSuggestionBtnState(false);
    }
}

bool EditorChatWindow::requestIsRunning() const
{
    return mSpinner->isVisible();
}

void EditorChatWindow::cancelRunningRequest()
{
    setSuggestionBtnState(false);
    ui->pushButton_cancel->setVisible(false);
    mSpinner->setVisible(false);
    ui->lineEdit_instruction->setDisabled(false);
    ui->lineEdit_instruction->setFocus();

    mRequestTimer->stop();

    emit cancelRequest(mTextEditor);
}

//-------------------------------------------------------------------
// EditorChat
//-------------------------------------------------------------------
EditorChat::EditorChat():
    QObject()
    , mCurrentWindow(nullptr)
    , mEditorPosition(0)
    , mHisoryInstCount(20)
{
    mInstHistory = CHAT_DB.latestUniqueInstructions(mHisoryInstCount);
}

EditorChat *EditorChat::instance()
{
    static EditorChat ec;
    return &ec;
}

void EditorChat::adjustCursor(QTextCursor &textCursor)
{
    // 当前行为非空行时
    if (!textCursor.block().text().trimmed().isEmpty())
    {
        textCursor.movePosition(QTextCursor::StartOfBlock);

        // 向上移动一行
        if (textCursor.movePosition(QTextCursor::Up))
        {
            // 上面一行非空时移动再移动回来到之前行开头
            if (!textCursor.block().text().trimmed().isEmpty())
            {
                textCursor.movePosition(QTextCursor::Down);
                textCursor.movePosition(QTextCursor::StartOfBlock);
            }
        }
        // 如果上面没有行了，移动到当前行开头
        else
        {
            textCursor.movePosition(QTextCursor::StartOfBlock);
        }
    }
}

void EditorChat::openChatWindow()
{
    auto textEditorWidget = TextEditor::BaseTextEditor::currentTextEditor()->editorWidget();
    if (!textEditorWidget)
        return;

    if (mCurrentWindow)
    {
        if (textEditorWidget == mCurrentWindow->textEditor())
        {
            if (mCurrentWindow->hasFocus())
            {
                textEditorWidget->setFocus();
                return;
            }
            else
            {
                mCurrentWindow->activateWindow();
                mCurrentWindow->activateInput();

                return;
            }
        }
        else
        {
            mCurrentWindow->close();
            mCurrentWindow = nullptr;
        }
    }

    textEditorWidget->clearSuggestion();

    mCurrentWindow = new EditorChatWindow(mInstHistory);
    mCurrentWindow->setCurrentModel(USER_OP.editorChatModelTitle);

    QString uuid = QUuid::createUuid().toString();
    mCurrentWindow->setProperty("uuid", uuid);
    mChatWindowEditors.insert(uuid, textEditorWidget);

    connect(mCurrentWindow, &EditorChatWindow::modelChanged, this, [=](QString modelTitle){
        USER_OP.editorChatModelTitle = modelTitle;
    });
    connect(mCurrentWindow, &EditorChatWindow::destroyed, this, &EditorChat::windowClosed);
    connect(mCurrentWindow, &EditorChatWindow::sendInstructionText, this, &EditorChat::setInstructionInfo);
    connect(textEditorWidget->verticalScrollBar(), &QScrollBar::valueChanged,
            mCurrentWindow, &EditorChatWindow::updatePosition);
    connect(mCurrentWindow, &EditorChatWindow::cancelRequest, this, &EditorChat::cancelRunningRequest);
}

EditorChatWindow *EditorChat::currentChatWindow() const
{
    return mCurrentWindow;
}

QString EditorChat::instructionText() const
{
    return mInstructionText;
}

ModelParam EditorChat::instructionParam() const
{
    return mModelParam;
}

LanguageServerProtocol::Position EditorChat::lspPosition() const
{
    return mLspPosition;
}

int EditorChat::editorPosition() const
{
    return mEditorPosition;
}

void EditorChat::replyStateChanged(bool running)
{
    if (mCurrentWindow)
    {
        mCurrentWindow->replyStateChanged(running);
    }
}

QStringList EditorChat::instructionHistory() const
{
    return mInstHistory;
}

void EditorChat::setInstructionInfo(const QString &instText, ModelParam modelParam)
{
    if (!mCurrentWindow)
        return;

    if (instText.isEmpty())
        return;

    mInstructionText = instText;
    mModelParam = modelParam;

    // 将光标移动到上一行
    auto textEditorWidget = mCurrentWindow->textEditor();
    {
        QTextCursor textCursor = textEditorWidget->textCursor();
        adjustCursor(textCursor);

        // 更新文本编辑器的光标
        textEditorWidget->setTextCursor(textCursor);

        mLspPosition = LanguageServerProtocol::Position(textCursor);
        mEditorPosition = textEditorWidget->position();
    }

    // 记录指令历史
    remeberInstructionHistory(instText);

    emit requestChatSuggestion(textEditorWidget);
}

void EditorChat::windowClosed(QObject *obj)
{
    mInstructionText.clear();
    mModelParam = CodeBooster::ModelParam();
    mLspPosition = LanguageServerProtocol::Position();
    mEditorPosition = 0;

    QString uuid = obj->property("uuid").toString();
    if (mChatWindowEditors.contains(uuid))
    {
        emit cancelRunningRequest(mChatWindowEditors.take(uuid));
    }

    if (obj == mCurrentWindow)
    {
        mCurrentWindow = nullptr;
    }
}

void EditorChat::remeberInstructionHistory(const QString &instText)
{
    if (instText.isEmpty())
        return;

    if (!mInstHistory.isEmpty())
    {
        if (mInstHistory.contains(instText))
            mInstHistory.removeAll(instText);

        mInstHistory.append(instText);
    }
    else
    {
        mInstHistory.append(instText);
    }

    int maximumCount = mHisoryInstCount;
    if (mInstHistory.size() == maximumCount)
    {
        mInstHistory.removeFirst();
    }

    // 持久化保存到数据库
    CHAT_DB.saveInstruction(instText);
}

}
