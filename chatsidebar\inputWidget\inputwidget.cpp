#include "inputwidget.h"

#include <QTextBlock>
#include <QDebug>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QRegularExpression>
#include <QScrollBar>
#include <QCheckBox>
#include <QMimeData>
#include <QTimer>
#include <QDragEnterEvent>
#include <QDragMoveEvent>
#include <QDropEvent>

#include <utils/utilsicons.h>
#include <texteditor/texteditor.h>
#include <utils/stylehelper.h>
#include <coreplugin/editormanager/editormanager.h>

#include "common/codeboostericons.h"
#include "common/widgettheme.h"
#include "common/codeboosterutils.h"
#include "common/useroptions.h"

#include "pluginsettings/codeboostersettings.h"

#include "../contextitemwidget.h"
#include "codesnippetwidget.h"
#include "customtextedit.h"
#include "codeboosterplugin.h"

using namespace TextEditor;


namespace CodeBooster::Internal{

/***************************************************************************************************
 * @brief InputWidget::InputWidget
 * @param parent
 ****************************************************************************************************/
InputWidget::InputWidget(QWidget *parent) : QFrame(parent)
    , mMainInputContainer(new QFrame(this))
    , mShowSnippet(USER_OP.showEditorSelection)
    , mInStreaming(false)
    , mToolBar(nullptr)
    , mFocusEditorAction(nullptr)
    , mContextBuilderAction(nullptr)
{
    setObjectName("InputWidget");
    setAcceptDrops(true);

    //this->setStyleSheet("QFrame#InputWidget {border: 1px solid red; background-color: green;}");

    // 获取当前的背景颜色
    {
        QPalette pal = palette();
        QColor bgColor = pal.color(QPalette::Base);
        mBgColorStr = bgColor.name();
    }

    // 初始化顶层布局
    QVBoxLayout *topLayout = new QVBoxLayout(this);
    topLayout->setContentsMargins(0, 0, 0, 0);
    topLayout->setSpacing(0);

    // 初始化顶部工具栏
    {
        mToolBar = new QToolBar(this);
        mToolBar->setObjectName("mToolBar");
        mToolBar->setIconSize({12, 12});
        mToolBar->setStyleSheet(CB_THEME.SS_InputWidget_ToolBar);

        QVBoxLayout *containerLayout = new QVBoxLayout();
        containerLayout->addWidget(mToolBar);

        containerLayout->setContentsMargins(0, 0, 0, 0);
        topLayout->addLayout(containerLayout, 0);

        // 使用当前文件
        {
            mUserCurrentFileAction = new QAction(Utils::Icons::PLUS_TOOLBAR.icon(), "使用当前文件", this);
            connect(mUserCurrentFileAction, &QAction::triggered, this, &InputWidget::onUseCurrentFileClicked);
            mToolBar->addAction(mUserCurrentFileAction);
            QLabel *currentFileLabel = new QLabel(this);
            currentFileLabel->setText("使用当前文件");
            mToolBar->addWidget(currentFileLabel);
        }

        // 上下文构建器
        {
            mContextBuilderAction = new QAction(ICON_CONTEXTBUILDER.icon(), "上下文生成器", this);
            connect(mContextBuilderAction, &QAction::triggered, this, &InputWidget::showContextBuilder);
            mToolBar->addAction(mContextBuilderAction);
        }

        // 编辑器专注模式
        {
            mFocusEditorAction = new QAction(ENLARGE_ICON.icon(), "扩大", this);
            mFocusEditorAction->setCheckable(true);
            mFocusEditorAction->setChecked(false);
            connect(mFocusEditorAction, &QAction::triggered, this, [=](bool checked){
                inputFocusModeChanged(checked);
                mTextEdit->setFocusMode(checked);
            });
            QWidget *spacer = new QWidget();
            spacer->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

            mToolBar->addWidget(spacer);
            mToolBar->addAction(mFocusEditorAction);
        }
    }

    // 初输入区域布局控件
    mMainInputContainer->setObjectName("mMainInputContainer");
    topLayout->addWidget(mMainInputContainer, 1);
    QVBoxLayout *inputLayout = new QVBoxLayout(mMainInputContainer);
    inputLayout->setContentsMargins(0, 0, 0, 0);
    inputLayout->setSpacing(0);

    // 初始化上下文控件
    {
        mContextItemContainer = new ContextItemContainer(this);
        mContextItemContainer->setVisible(false);

        connect(mContextItemContainer, &ContextItemContainer::contextRemoved, this, [this](const ContextItem &context){
            mTextEdit->removeContextTag(context);
        });

        inputLayout->addWidget(mContextItemContainer);
    }

    // 初始化代码段控件容器
    {
        mSnippetContainerLayout = new QVBoxLayout();
        mSnippetContainerLayout->setContentsMargins(0, 0, 0, 0);
        mSnippetContainerLayout->setSpacing(0);
        inputLayout->addLayout(mSnippetContainerLayout, 0);

        connect(CodeBoosterPlugin::instance(), &CodeBoosterPlugin::documentSelectionChanged, this, &InputWidget::onShowCodeSnippet);
    }

    // 初始化文本编辑控件
    {
        QHBoxLayout *textEditLayout = new QHBoxLayout();
        textEditLayout->setSpacing(0);
        textEditLayout->setContentsMargins(0, 0, 0, 0);

        {
            mTextEdit = new CustomTextEdit(this);
            connect(mTextEdit, &CustomTextEdit::focusChange, this, &InputWidget::onTextEditFocusChange);
            connect(mTextEdit, &CustomTextEdit::sendMessage, this, [this]{
                if (!mInStreaming)
                {
                    onSendButtonClicked();

                }
            });
            connect(mTextEdit, &CustomTextEdit::newChat, this, [this]{
                QString message = mTextEdit->toPlainText().simplified();
                if (!mInStreaming)
                {
                    emit createNewChat();
                }
            });
            connect(mTextEdit, &CustomTextEdit::focusModeShortcutPress, this, [this](){
                mFocusEditorAction->trigger();
            });
            connect(mTextEdit, &CustomTextEdit::newContextItem, this, [this](const ContextItem &item){
                // 第一次添加上下文时显示控件
                mContextItemContainer->setVisible(true);
                mContextItemContainer->newContextItemWgt(item);
            });

            textEditLayout->addWidget(mTextEdit);
        }

        inputLayout->addLayout(textEditLayout, 1);
    }

    // 初始化样式
    onTextEditFocusChange(false);
}

InputWidget::~InputWidget()
{

}

void InputWidget::waitingForReceiveMsg()
{
    mInStreaming = true;
}

void InputWidget::messageReceiveFinished()
{
    mInStreaming = false;
}

void InputWidget::setShowEditorSelection(bool show)
{
    mShowSnippet = show;
    if (show == false)
    {
        // 清除所有非置顶的代码片段
        for (auto it = mCodeSnippetWidgets.begin(); it != mCodeSnippetWidgets.end();) {
            CodeSnippetWidget* widget = *it;
            if (!widget->isPinned()) {
                mSnippetContainerLayout->removeWidget(widget);
                widget->deleteLater();
                it = mCodeSnippetWidgets.erase(it);
            } else {
                ++it;
            }
        }
    }
}

void InputWidget::activateInput()
{
    mTextEdit->setFocus();

    // 自动显示文本编辑器选中的代码
    // 检查是否有非置顶的代码片段显示当前选中内容
    bool hasActiveSnippet = false;
    for (CodeSnippetWidget* widget : mCodeSnippetWidgets) {
        if (!widget->isPinned() && !widget->codeSnippet().isEmpty()) {
            hasActiveSnippet = true;
            break;
        }
    }

    if (!hasActiveSnippet) {
        auto textEditor = BaseTextEditor::currentTextEditor();
        if (!textEditor)
            return;
        TextEditorWidget *widget = textEditor->editorWidget();

        QString fileName = widget->textDocument()->filePath().fileName();
        QString snippet = widget->selectedText();

        if(!snippet.isEmpty() && USER_OP.showEditorSelection) {
            // 计算当前选中文本的行号
            QTextCursor cursor = widget->textCursor();
            int startLine = 1, endLine = 1;
            if (cursor.hasSelection()) {
                int selectionStart = cursor.selectionStart();
                int selectionEnd = cursor.selectionEnd();

                QTextCursor startCursor(widget->document());
                startCursor.setPosition(selectionStart);
                startLine = startCursor.blockNumber() + 1;

                QTextCursor endCursor(widget->document());
                endCursor.setPosition(selectionEnd);
                endLine = endCursor.blockNumber() + 1;
            }
            onShowCodeSnippet(fileName, snippet, startLine, endLine);
        }
    }
}

void InputWidget::setText(const QString &text)
{
    mTextEdit->setPlainText(text);
}

void InputWidget::onSendButtonClicked()
{
    if (!mInStreaming)
    {
        QString message = mTextEdit->toPlainText().trimmed();

        // 收集所有可见的代码片段
        QStringList codeSnippets;
        for (auto it = mCodeSnippetWidgets.begin(); it != mCodeSnippetWidgets.end();) {
            CodeSnippetWidget* widget = *it;
            if (widget->isVisible() && !widget->codeSnippet().isEmpty()) {
                codeSnippets.append(widget->codeSnippet());

                // 发送后清除
                {
                    mSnippetContainerLayout->removeWidget(widget);
                    widget->deleteLater();
                    it = mCodeSnippetWidgets.erase(it);
                    continue;
                }
            }
            ++it;
        }

        // 将代码片段添加到消息前面
        if (!codeSnippets.isEmpty()) {
            message.prepend(QString("%1\n\n").arg(codeSnippets.join("\n\n")));
        }

        // 获取上下文
        QList<ContextItem> contexts = mContextItemContainer->allContext();

        if (message.isEmpty() && contexts.isEmpty())
            return;

        if (mFocusEditorAction->isChecked())
        {
            // 发送消息后退出专注编辑模式
            mFocusEditorAction->trigger();
        }

        setText(QString());
        waitingForReceiveMsg();

        mContextItemContainer->clearContext();
        emit sendUserMessage(message, contexts);
    }
}

void InputWidget::currentRequestStoped()
{

}

QList<ContextItem> InputWidget::contexts() const
{
    return mContextItemContainer->allContext();
}

void InputWidget::addContextItem(const ContextItem &context)
{
    mContextItemContainer->newContextItemWgt(context);
}

bool InputWidget::event(QEvent *event)
{
    if (event->type() == QEvent::Show)
    {
        QTimer::singleShot(10, [this]() {
            activateInput();
        });
    }

    return QFrame::event(event);
}


void InputWidget::onShowCodeSnippet(const QString &fileName, const QString &text, int startLine, int endLine)
{
    if (!USER_OP.showEditorSelection)
        return;

    if (!mShowSnippet)
        return;

    // 如果文本为空，清除所有非置顶的代码片段
    if (text.trimmed().isEmpty()) {
        for (auto it = mCodeSnippetWidgets.begin(); it != mCodeSnippetWidgets.end();) {
            CodeSnippetWidget* widget = *it;
            if (!widget->isPinned()) {
                mSnippetContainerLayout->removeWidget(widget);
                widget->deleteLater();
                it = mCodeSnippetWidgets.erase(it);
            } else {
                ++it;
            }
        }
        return;
    }

    // 查找是否已有置顶的代码片段
    bool hasPinnedWidget = false;
    for (CodeSnippetWidget* widget : mCodeSnippetWidgets) {
        if (widget->isPinned()) {
            hasPinnedWidget = true;
            break;
        }
    }

    // 如果有置顶的代码片段，需要创建新的代码片段控件
    if (hasPinnedWidget) {
        // 先清除所有非置顶的代码片段
        for (auto it = mCodeSnippetWidgets.begin(); it != mCodeSnippetWidgets.end();) {
            CodeSnippetWidget* widget = *it;
            if (!widget->isPinned()) {
                mSnippetContainerLayout->removeWidget(widget);
                widget->deleteLater();
                it = mCodeSnippetWidgets.erase(it);
            } else {
                ++it;
            }
        }

        // 创建新的代码片段控件
        createNewCodeSnippetWidget(fileName, text, startLine, endLine);
    } else {
        // 没有置顶的代码片段，使用或创建一个普通的代码片段控件
        CodeSnippetWidget* activeWidget = nullptr;

        // 查找第一个非置顶的代码片段控件
        for (CodeSnippetWidget* widget : mCodeSnippetWidgets) {
            if (!widget->isPinned()) {
                activeWidget = widget;
                break;
            }
        }

        // 如果没有找到，创建一个新的
        if (!activeWidget) {
            activeWidget = createNewCodeSnippetWidget(fileName, text, startLine, endLine);
        } else {
            activeWidget->showCodeSnippet(fileName, text, startLine, endLine);
        }
    }
}

void InputWidget::onTextEditFocusChange(bool focus)
{
    QString styleSheetStr;

    if (focus)
    {
        styleSheetStr = QString("QFrame#InputWidget { border: 1px solid #005BBE; background-color: %1; }").arg(CB_THEME.Color_NomalBackground);
    }
    else
    {
        styleSheetStr = QString("QFrame#InputWidget { border: 1px solid #A8A8A9; background-color: %1; }").arg(CB_THEME.Color_NomalBackground);
    }

    // mMainInputContainer->setStyleSheet(styleSheetStr);
    setStyleSheet(styleSheetStr);
}

void InputWidget::onUseCurrentFileClicked(bool checked)
{
    using namespace Core;

    if (const IDocument *document = EditorManager::currentDocument())
    {
        bool ok = true;
        ContextItem item = ContextItem::buildFileContextFromFilePath(document->filePath(), ok);

        if (ok)
        {
            mTextEdit->addContextItem(item);
        }
    }
}

CodeSnippetWidget* InputWidget::createNewCodeSnippetWidget(const QString &fileName, const QString &text, int startLine, int endLine)
{
    CodeSnippetWidget* newWidget = new CodeSnippetWidget(this);

    // 连接信号槽
    connect(newWidget, &CodeSnippetWidget::closeRequested, this, [this, newWidget]() {
        // 从布局和列表中移除控件
        mSnippetContainerLayout->removeWidget(newWidget);
        mCodeSnippetWidgets.removeOne(newWidget);
        newWidget->deleteLater();
    });

    connect(newWidget, &CodeSnippetWidget::pinStateChanged, this, [this](bool pinned) {
        // 置顶状态变化时的处理逻辑已在onShowCodeSnippet中处理
        Q_UNUSED(pinned)
    });

    // 添加到布局和列表
    mSnippetContainerLayout->addWidget(newWidget);
    mCodeSnippetWidgets.append(newWidget);

    // 显示代码片段
    newWidget->showCodeSnippet(fileName, text, startLine, endLine);

    return newWidget;
}

}
