#ifndef NETWORKREQUESTMANAGER_H
#define NETWORKREQUESTMANAGER_H

#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QTimer>
#include <QJsonObject>
#include <QJsonDocument>
#include <QJsonArray>
#include <QByteArray>
#include <QString>
#include <QObject>

#include "pluginsettings/codeboostersettings.h"

namespace CodeBooster {
namespace Internal {

/**
 * @brief The NetworkRequestManager class 统一的网络请求管理层，所有的网络请求应通过此类发出
 */
class NetworkRequestManager : public QObject
{
    Q_OBJECT

public:
    explicit NetworkRequestManager(QObject *parent = nullptr);
    ~NetworkRequestManager();

    void sendRequest(ModelParam param, const QJsonArray &messages, bool stream, int timeout = 5000);
    void cancelRequest();

signals:
    void requestFinished();
    void requestTimeout();
    void streamReceived(const QString &content);
    void errorOccurred(const QStringList &errorInfo);
    void messageReceived(const QStringList &msgs);

private slots:
    void replyFinished();
    void handleStreamReceived();
    void onRequestTimeout();


private:
    QSharedPointer<QNetworkAccessManager>  manager;
    QSharedPointer<QNetworkReply> reply;
    QTimer timeoutTimer;
    QString response;
};

} // namespace Internal
} // namespace CodeBooster

#endif // NETWORKREQUESTMANAGER_H
