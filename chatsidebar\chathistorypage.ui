<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ChatHistoryPage</class>
 <widget class="QWidget" name="ChatHistoryPage">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>423</width>
    <height>729</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_4">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="1,0">
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>对话列表</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_deleteAll">
       <property name="text">
        <string>删除全部</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <property name="spacing">
      <number>6</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="Utils::FancyLineEdit" name="lineEdit_search"/>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_search">
         <property name="text">
          <string>搜索</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_4">
       <item>
        <widget class="QLabel" name="label_info">
         <property name="text">
          <string>TextLabel</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QStackedWidget" name="stackedWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="page_sessionList">
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QScrollArea" name="scrollArea">
         <property name="widgetResizable">
          <bool>true</bool>
         </property>
         <widget class="QWidget" name="scrollAreaWidgetContents">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>0</y>
            <width>403</width>
            <height>587</height>
           </rect>
          </property>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="page_searchResult">
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QScrollArea" name="scrollArea_searchList">
         <property name="widgetResizable">
          <bool>true</bool>
         </property>
         <widget class="QWidget" name="scrollAreaWidgetContents_2">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>0</y>
            <width>344</width>
            <height>427</height>
           </rect>
          </property>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <item>
      <widget class="QPushButton" name="pushButton_check">
       <property name="text">
        <string>选择</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_selectAll">
       <property name="text">
        <string>全选</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_invertSelection">
       <property name="text">
        <string>反选</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_deleteSelection">
       <property name="text">
        <string>删除选中</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>Utils::FancyLineEdit</class>
   <extends>QLineEdit</extends>
   <header>utils/fancylineedit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
